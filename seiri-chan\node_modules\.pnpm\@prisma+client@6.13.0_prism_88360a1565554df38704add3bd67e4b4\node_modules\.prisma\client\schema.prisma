// schema.prisma
datasource db {
  provider = "sqlite"
  url      = "file:./seiri.db"
}

generator client {
  provider = "prisma-client-js"
}

// --- Enums for Type Safety ---

enum MediaTaskStatus {
  PENDING // 任务已创建，等待处理
  SCANNING // 正在扫描文件和获取元数据
  ANALYSIS_FAILED // 分析失败（如找不到匹配媒体）
  ANALYSIS_AWAITING_USER // 分析完成，但存在需要用户确认的情况
  PROCESSING // 正在执行文件操作
  COMPLETED // 所有文件任务成功
  CONFIRMATION_REQUIRED // 部分文件任务成功，个别需要用户确认
  FAILED // 任务因可恢复错误失败
  FATAL_ERROR // 移动模式下出现严重错误
}

enum FileTaskStatus {
  PENDING // 等待分析
  IGNORED // 未命中匹配规则或不在AI识别结果中，不处理
  MAPPED // 已成功映射到目标，等待处理
  MISSING // AI给出了不存在的源文件路径
  PROCESSING // 文件复制或移动中
  COMPLETED // 整理成功
  FILE_NOT_EXIST // 源文件在处理时已不存在
  FAILED // 文件整理失败
  FATAL_ERROR // 移动模式出现严重错误
  CONFIRMATION_REQUIRED // AI置信度低或存在冲突，等待用户确认
}

enum FileType {
  VIDEO_MAIN // 正片视频
  VIDEO_SPECIAL // 特典视频 (SP, OVA)
  VIDEO_TRAILER // 预告片 (Trailer, PV)
  VIDEO_EXTRA // OP, ED 等
  VIDEO_UNKNOWN // 未知类型视频
  SUBTITLE // 字幕文件
  FONT // 字体文件
}

enum MediaType {
  ANIME
  TV
  MOVIE
  ANIME_MOVIE
}

enum FileOperation {
  HARDLINK
  SOFTLINK
  COPY
  MOVE
  SKIP // for dry run
}

// --- Models ---

// 媒体整理的顶层任务
model MediaTask {
  id            String          @id @default(uuid())
  sourcePath    String
  // --- 状态与结果 ---
  status        MediaTaskStatus @default(PENDING)
  type          MediaType // "anime", "tv", "movie", "anime_movie"
  fileOperation FileOperation // 整理任务级的默认文件操作
  createdAt     DateTime        @default(now())
  finishedAt    DateTime?

  // --- 关联的元数据 ---
  tmdbId      Int?
  displayName String?
  displayYear Int?
  posterPath  String?

  fileTasks FileTask[]
}

// 单个文件的任务记录
model FileTask {
  id              String        @id @default(uuid())
  sourcePath      String // 原文件相对路径，需采用复合约束
  associatedFiles Json? // 关联文件数组
  fileOperation   FileOperation
  // --- 文件元数据 ---
  fileSize        BigInt?
  duration        Float?
  resolution      String?
  // --- 文件类型 ---
  fileType        FileType      @default(VIDEO_MAIN)

  // --- 分析与识别结果 ---
  status FileTaskStatus @default(PENDING)

  // --- 目标信息 ---
  targetSeason  Int? // 目标季号(仅剧集)
  targetEpisode Int? // 目标集号(仅剧集)
  targetPath    String? // 最终生成的目标相对路径

  // --- 错误与重试 ---
  // 用于存储结构化错误信息, e.g., { "code": "E_NO_SPACE", "message": "...", "details": "..." }
  errorMessage Json?

  // --- 关联 ---
  mediaTask   MediaTask @relation(fields: [mediaTaskId], references: [id])
  mediaTaskId String

  @@unique([mediaTaskId, sourcePath]) // 定义复合唯一键
}

// TMDB 搜索结果缓存
model TmdbSearchCache {
  query     String   @id // 搜索关键词作为主键
  data      String // 完整的、拼接后的搜索结果JSON
  createdAt DateTime @default(now())
}

enum TmdbMediaType {
  MOVIE
  TV
}

// TMDB 媒体元数据缓存 (电影或电视剧)
model TmdbMediaCache {
  tmdbId    Int           @id // TMDB ID 作为主键
  mediaType TmdbMediaType // "movie" or "tv"
  data      String // 完整的媒体元数据JSON
  createdAt DateTime      @default(now())

  // 关联的季缓存（仅电视剧有效）
  seasons TmdbSeasonCache[]
}

// TMDB 季数据缓存
model TmdbSeasonCache {
  // 复合主键：电视剧ID + 季度号
  tvId         Int
  seasonNumber Int
  data         String // 完整的季数据JSON
  createdAt    DateTime @default(now())

  // 关联的电视剧缓存，设置级联删除
  tvShow TmdbMediaCache @relation(fields: [tvId], references: [tmdbId], onDelete: Cascade)

  @@id([tvId, seasonNumber])
}
