
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('@prisma/client/runtime/edge.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.MediaTaskScalarFieldEnum = {
  id: 'id',
  sourcePath: 'sourcePath',
  status: 'status',
  type: 'type',
  fileOperation: 'fileOperation',
  createdAt: 'createdAt',
  finishedAt: 'finishedAt',
  tmdbId: 'tmdbId',
  displayName: 'displayName',
  displayYear: 'displayYear',
  posterPath: 'posterPath'
};

exports.Prisma.FileTaskScalarFieldEnum = {
  id: 'id',
  sourcePath: 'sourcePath',
  associatedFiles: 'associatedFiles',
  fileOperation: 'fileOperation',
  fileSize: 'fileSize',
  duration: 'duration',
  resolution: 'resolution',
  fileType: 'fileType',
  status: 'status',
  targetSeason: 'targetSeason',
  targetEpisode: 'targetEpisode',
  targetPath: 'targetPath',
  errorMessage: 'errorMessage',
  mediaTaskId: 'mediaTaskId'
};

exports.Prisma.TmdbSearchCacheScalarFieldEnum = {
  query: 'query',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.TmdbMediaCacheScalarFieldEnum = {
  tmdbId: 'tmdbId',
  mediaType: 'mediaType',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.TmdbSeasonCacheScalarFieldEnum = {
  tvId: 'tvId',
  seasonNumber: 'seasonNumber',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.MediaTaskStatus = exports.$Enums.MediaTaskStatus = {
  PENDING: 'PENDING',
  SCANNING: 'SCANNING',
  ANALYSIS_FAILED: 'ANALYSIS_FAILED',
  ANALYSIS_AWAITING_USER: 'ANALYSIS_AWAITING_USER',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR'
};

exports.FileTaskStatus = exports.$Enums.FileTaskStatus = {
  PENDING: 'PENDING',
  IGNORED: 'IGNORED',
  MAPPED: 'MAPPED',
  MISSING: 'MISSING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FILE_NOT_EXIST: 'FILE_NOT_EXIST',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED'
};

exports.FileType = exports.$Enums.FileType = {
  VIDEO_MAIN: 'VIDEO_MAIN',
  VIDEO_SPECIAL: 'VIDEO_SPECIAL',
  VIDEO_TRAILER: 'VIDEO_TRAILER',
  VIDEO_EXTRA: 'VIDEO_EXTRA',
  VIDEO_UNKNOWN: 'VIDEO_UNKNOWN',
  SUBTITLE: 'SUBTITLE',
  FONT: 'FONT'
};

exports.MediaType = exports.$Enums.MediaType = {
  ANIME: 'ANIME',
  TV: 'TV',
  MOVIE: 'MOVIE',
  ANIME_MOVIE: 'ANIME_MOVIE'
};

exports.FileOperation = exports.$Enums.FileOperation = {
  HARDLINK: 'HARDLINK',
  SOFTLINK: 'SOFTLINK',
  COPY: 'COPY',
  MOVE: 'MOVE',
  SKIP: 'SKIP'
};

exports.TmdbMediaType = exports.$Enums.TmdbMediaType = {
  MOVIE: 'MOVIE',
  TV: 'TV'
};

exports.Prisma.ModelName = {
  MediaTask: 'MediaTask',
  FileTask: 'FileTask',
  TmdbSearchCache: 'TmdbSearchCache',
  TmdbMediaCache: 'TmdbMediaCache',
  TmdbSeasonCache: 'TmdbSeasonCache'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Reponsitories\\Seiri-chan\\seiri-chan\\node_modules\\.pnpm\\@prisma+client@6.13.0_prism_88360a1565554df38704add3bd67e4b4\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "C:\\Users\\<USER>\\Reponsitories\\Seiri-chan\\seiri-chan\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../../../../.env"
  },
  "relativePath": "../../../../../../prisma",
  "clientVersion": "6.13.0",
  "engineVersion": "361e86d0ea4987e9f53a565309b3eed797a6bcbd",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "sqlite",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": null,
        "value": "file:./seiri.db"
      }
    }
  },
  "inlineSchema": "// schema.prisma\ndatasource db {\n  provider = \"sqlite\"\n  url      = \"file:./seiri.db\"\n}\n\ngenerator client {\n  provider = \"prisma-client-js\"\n}\n\n// --- Enums for Type Safety ---\n\nenum MediaTaskStatus {\n  PENDING // 任务已创建，等待处理\n  SCANNING // 正在扫描文件和获取元数据\n  ANALYSIS_FAILED // 分析失败（如找不到匹配媒体）\n  ANALYSIS_AWAITING_USER // 分析完成，但存在需要用户确认的情况\n  PROCESSING // 正在执行文件操作\n  COMPLETED // 所有文件任务成功\n  CONFIRMATION_REQUIRED // 部分文件任务成功，个别需要用户确认\n  FAILED // 任务因可恢复错误失败\n  FATAL_ERROR // 移动模式下出现严重错误\n}\n\nenum FileTaskStatus {\n  PENDING // 等待分析\n  IGNORED // 未命中匹配规则或不在AI识别结果中，不处理\n  MAPPED // 已成功映射到目标，等待处理\n  MISSING // AI给出了不存在的源文件路径\n  PROCESSING // 文件复制或移动中\n  COMPLETED // 整理成功\n  FILE_NOT_EXIST // 源文件在处理时已不存在\n  FAILED // 文件整理失败\n  FATAL_ERROR // 移动模式出现严重错误\n  CONFIRMATION_REQUIRED // AI置信度低或存在冲突，等待用户确认\n}\n\nenum FileType {\n  VIDEO_MAIN // 正片视频\n  VIDEO_SPECIAL // 特典视频 (SP, OVA)\n  VIDEO_TRAILER // 预告片 (Trailer, PV)\n  VIDEO_EXTRA // OP, ED 等\n  VIDEO_UNKNOWN // 未知类型视频\n  SUBTITLE // 字幕文件\n  FONT // 字体文件\n}\n\nenum MediaType {\n  ANIME\n  TV\n  MOVIE\n  ANIME_MOVIE\n}\n\nenum FileOperation {\n  HARDLINK\n  SOFTLINK\n  COPY\n  MOVE\n  SKIP // for dry run\n}\n\n// --- Models ---\n\n// 媒体整理的顶层任务\nmodel MediaTask {\n  id            String          @id @default(uuid())\n  sourcePath    String\n  // --- 状态与结果 ---\n  status        MediaTaskStatus @default(PENDING)\n  type          MediaType // \"anime\", \"tv\", \"movie\", \"anime_movie\"\n  fileOperation FileOperation // 整理任务级的默认文件操作\n  createdAt     DateTime        @default(now())\n  finishedAt    DateTime?\n\n  // --- 关联的元数据 ---\n  tmdbId      Int?\n  displayName String?\n  displayYear Int?\n  posterPath  String?\n\n  fileTasks FileTask[]\n}\n\n// 单个文件的任务记录\nmodel FileTask {\n  id              String        @id @default(uuid())\n  sourcePath      String // 原文件相对路径，需采用复合约束\n  associatedFiles Json? // 关联文件数组\n  fileOperation   FileOperation\n  // --- 文件元数据 ---\n  fileSize        BigInt?\n  duration        Float?\n  resolution      String?\n  // --- 文件类型 ---\n  fileType        FileType      @default(VIDEO_MAIN)\n\n  // --- 分析与识别结果 ---\n  status FileTaskStatus @default(PENDING)\n\n  // --- 目标信息 ---\n  targetSeason  Int? // 目标季号(仅剧集)\n  targetEpisode Int? // 目标集号(仅剧集)\n  targetPath    String? // 最终生成的目标相对路径\n\n  // --- 错误与重试 ---\n  // 用于存储结构化错误信息, e.g., { \"code\": \"E_NO_SPACE\", \"message\": \"...\", \"details\": \"...\" }\n  errorMessage Json?\n\n  // --- 关联 ---\n  mediaTask   MediaTask @relation(fields: [mediaTaskId], references: [id])\n  mediaTaskId String\n\n  @@unique([mediaTaskId, sourcePath]) // 定义复合唯一键\n}\n\n// TMDB 搜索结果缓存\nmodel TmdbSearchCache {\n  query     String   @id // 搜索关键词作为主键\n  data      String // 完整的、拼接后的搜索结果JSON\n  createdAt DateTime @default(now())\n}\n\nenum TmdbMediaType {\n  MOVIE\n  TV\n}\n\n// TMDB 媒体元数据缓存 (电影或电视剧)\nmodel TmdbMediaCache {\n  tmdbId    Int           @id // TMDB ID 作为主键\n  mediaType TmdbMediaType // \"movie\" or \"tv\"\n  data      String // 完整的媒体元数据JSON\n  createdAt DateTime      @default(now())\n\n  // 关联的季缓存（仅电视剧有效）\n  seasons TmdbSeasonCache[]\n}\n\n// TMDB 季数据缓存\nmodel TmdbSeasonCache {\n  // 复合主键：电视剧ID + 季度号\n  tvId         Int\n  seasonNumber Int\n  data         String // 完整的季数据JSON\n  createdAt    DateTime @default(now())\n\n  // 关联的电视剧缓存，设置级联删除\n  tvShow TmdbMediaCache @relation(fields: [tvId], references: [tmdbId], onDelete: Cascade)\n\n  @@id([tvId, seasonNumber])\n}\n",
  "inlineSchemaHash": "7ad91616e3943d5d34ad0950e89fdd7b823f5945a25fec7ac014b0d92452a9c7",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"MediaTask\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourcePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"MediaTaskStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"MediaType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fileOperation\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FileOperation\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"finishedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tmdbId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"displayName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"displayYear\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"posterPath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fileTasks\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FileTask\",\"nativeType\":null,\"relationName\":\"FileTaskToMediaTask\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"FileTask\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"nativeType\":null,\"default\":{\"name\":\"uuid\",\"args\":[4]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sourcePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"associatedFiles\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fileOperation\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"FileOperation\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fileSize\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"BigInt\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"duration\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Float\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"resolution\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"fileType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"FileType\",\"nativeType\":null,\"default\":\"VIDEO_MAIN\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"FileTaskStatus\",\"nativeType\":null,\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetSeason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetEpisode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"targetPath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"errorMessage\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mediaTask\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"MediaTask\",\"nativeType\":null,\"relationName\":\"FileTaskToMediaTask\",\"relationFromFields\":[\"mediaTaskId\"],\"relationToFields\":[\"id\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mediaTaskId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"mediaTaskId\",\"sourcePath\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"mediaTaskId\",\"sourcePath\"]}],\"isGenerated\":false},\"TmdbSearchCache\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"query\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"data\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"TmdbMediaCache\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"tmdbId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"mediaType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TmdbMediaType\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"data\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"seasons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TmdbSeasonCache\",\"nativeType\":null,\"relationName\":\"TmdbMediaCacheToTmdbSeasonCache\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"TmdbSeasonCache\":{\"dbName\":null,\"schema\":null,\"fields\":[{\"name\":\"tvId\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"seasonNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"data\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"nativeType\":null,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"nativeType\":null,\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"tvShow\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"TmdbMediaCache\",\"nativeType\":null,\"relationName\":\"TmdbMediaCacheToTmdbSeasonCache\",\"relationFromFields\":[\"tvId\"],\"relationToFields\":[\"tmdbId\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":{\"name\":null,\"fields\":[\"tvId\",\"seasonNumber\"]},\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"MediaTaskStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"SCANNING\",\"dbName\":null},{\"name\":\"ANALYSIS_FAILED\",\"dbName\":null},{\"name\":\"ANALYSIS_AWAITING_USER\",\"dbName\":null},{\"name\":\"PROCESSING\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null},{\"name\":\"CONFIRMATION_REQUIRED\",\"dbName\":null},{\"name\":\"FAILED\",\"dbName\":null},{\"name\":\"FATAL_ERROR\",\"dbName\":null}],\"dbName\":null},\"FileTaskStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":null},{\"name\":\"IGNORED\",\"dbName\":null},{\"name\":\"MAPPED\",\"dbName\":null},{\"name\":\"MISSING\",\"dbName\":null},{\"name\":\"PROCESSING\",\"dbName\":null},{\"name\":\"COMPLETED\",\"dbName\":null},{\"name\":\"FILE_NOT_EXIST\",\"dbName\":null},{\"name\":\"FAILED\",\"dbName\":null},{\"name\":\"FATAL_ERROR\",\"dbName\":null},{\"name\":\"CONFIRMATION_REQUIRED\",\"dbName\":null}],\"dbName\":null},\"FileType\":{\"values\":[{\"name\":\"VIDEO_MAIN\",\"dbName\":null},{\"name\":\"VIDEO_SPECIAL\",\"dbName\":null},{\"name\":\"VIDEO_TRAILER\",\"dbName\":null},{\"name\":\"VIDEO_EXTRA\",\"dbName\":null},{\"name\":\"VIDEO_UNKNOWN\",\"dbName\":null},{\"name\":\"SUBTITLE\",\"dbName\":null},{\"name\":\"FONT\",\"dbName\":null}],\"dbName\":null},\"MediaType\":{\"values\":[{\"name\":\"ANIME\",\"dbName\":null},{\"name\":\"TV\",\"dbName\":null},{\"name\":\"MOVIE\",\"dbName\":null},{\"name\":\"ANIME_MOVIE\",\"dbName\":null}],\"dbName\":null},\"FileOperation\":{\"values\":[{\"name\":\"HARDLINK\",\"dbName\":null},{\"name\":\"SOFTLINK\",\"dbName\":null},{\"name\":\"COPY\",\"dbName\":null},{\"name\":\"MOVE\",\"dbName\":null},{\"name\":\"SKIP\",\"dbName\":null}],\"dbName\":null},\"TmdbMediaType\":{\"values\":[{\"name\":\"MOVIE\",\"dbName\":null},{\"name\":\"TV\",\"dbName\":null}],\"dbName\":null}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {}
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

