
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model MediaTask
 * 
 */
export type MediaTask = $Result.DefaultSelection<Prisma.$MediaTaskPayload>
/**
 * Model FileTask
 * 
 */
export type FileTask = $Result.DefaultSelection<Prisma.$FileTaskPayload>
/**
 * Model TmdbSearchCache
 * 
 */
export type TmdbSearchCache = $Result.DefaultSelection<Prisma.$TmdbSearchCachePayload>
/**
 * Model TmdbMediaCache
 * 
 */
export type TmdbMediaCache = $Result.DefaultSelection<Prisma.$TmdbMediaCachePayload>
/**
 * Model TmdbSeasonCache
 * 
 */
export type TmdbSeasonCache = $Result.DefaultSelection<Prisma.$TmdbSeasonCachePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const MediaTaskStatus: {
  PENDING: 'PENDING',
  SCANNING: 'SCANNING',
  ANALYSIS_FAILED: 'ANALYSIS_FAILED',
  ANALYSIS_AWAITING_USER: 'ANALYSIS_AWAITING_USER',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR'
};

export type MediaTaskStatus = (typeof MediaTaskStatus)[keyof typeof MediaTaskStatus]


export const FileTaskStatus: {
  PENDING: 'PENDING',
  IGNORED: 'IGNORED',
  MAPPED: 'MAPPED',
  MISSING: 'MISSING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FILE_NOT_EXIST: 'FILE_NOT_EXIST',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED'
};

export type FileTaskStatus = (typeof FileTaskStatus)[keyof typeof FileTaskStatus]


export const FileType: {
  VIDEO_MAIN: 'VIDEO_MAIN',
  VIDEO_SPECIAL: 'VIDEO_SPECIAL',
  VIDEO_TRAILER: 'VIDEO_TRAILER',
  VIDEO_EXTRA: 'VIDEO_EXTRA',
  VIDEO_UNKNOWN: 'VIDEO_UNKNOWN',
  SUBTITLE: 'SUBTITLE',
  FONT: 'FONT'
};

export type FileType = (typeof FileType)[keyof typeof FileType]


export const MediaType: {
  ANIME: 'ANIME',
  TV: 'TV',
  MOVIE: 'MOVIE',
  ANIME_MOVIE: 'ANIME_MOVIE'
};

export type MediaType = (typeof MediaType)[keyof typeof MediaType]


export const FileOperation: {
  HARDLINK: 'HARDLINK',
  SOFTLINK: 'SOFTLINK',
  COPY: 'COPY',
  MOVE: 'MOVE',
  SKIP: 'SKIP'
};

export type FileOperation = (typeof FileOperation)[keyof typeof FileOperation]


export const TmdbMediaType: {
  MOVIE: 'MOVIE',
  TV: 'TV'
};

export type TmdbMediaType = (typeof TmdbMediaType)[keyof typeof TmdbMediaType]

}

export type MediaTaskStatus = $Enums.MediaTaskStatus

export const MediaTaskStatus: typeof $Enums.MediaTaskStatus

export type FileTaskStatus = $Enums.FileTaskStatus

export const FileTaskStatus: typeof $Enums.FileTaskStatus

export type FileType = $Enums.FileType

export const FileType: typeof $Enums.FileType

export type MediaType = $Enums.MediaType

export const MediaType: typeof $Enums.MediaType

export type FileOperation = $Enums.FileOperation

export const FileOperation: typeof $Enums.FileOperation

export type TmdbMediaType = $Enums.TmdbMediaType

export const TmdbMediaType: typeof $Enums.TmdbMediaType

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more MediaTasks
 * const mediaTasks = await prisma.mediaTask.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  const U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more MediaTasks
   * const mediaTasks = await prisma.mediaTask.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.mediaTask`: Exposes CRUD operations for the **MediaTask** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more MediaTasks
    * const mediaTasks = await prisma.mediaTask.findMany()
    * ```
    */
  get mediaTask(): Prisma.MediaTaskDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.fileTask`: Exposes CRUD operations for the **FileTask** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more FileTasks
    * const fileTasks = await prisma.fileTask.findMany()
    * ```
    */
  get fileTask(): Prisma.FileTaskDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tmdbSearchCache`: Exposes CRUD operations for the **TmdbSearchCache** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TmdbSearchCaches
    * const tmdbSearchCaches = await prisma.tmdbSearchCache.findMany()
    * ```
    */
  get tmdbSearchCache(): Prisma.TmdbSearchCacheDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tmdbMediaCache`: Exposes CRUD operations for the **TmdbMediaCache** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TmdbMediaCaches
    * const tmdbMediaCaches = await prisma.tmdbMediaCache.findMany()
    * ```
    */
  get tmdbMediaCache(): Prisma.TmdbMediaCacheDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.tmdbSeasonCache`: Exposes CRUD operations for the **TmdbSeasonCache** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more TmdbSeasonCaches
    * const tmdbSeasonCaches = await prisma.tmdbSeasonCache.findMany()
    * ```
    */
  get tmdbSeasonCache(): Prisma.TmdbSeasonCacheDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.13.0
   * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    MediaTask: 'MediaTask',
    FileTask: 'FileTask',
    TmdbSearchCache: 'TmdbSearchCache',
    TmdbMediaCache: 'TmdbMediaCache',
    TmdbSeasonCache: 'TmdbSeasonCache'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "mediaTask" | "fileTask" | "tmdbSearchCache" | "tmdbMediaCache" | "tmdbSeasonCache"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      MediaTask: {
        payload: Prisma.$MediaTaskPayload<ExtArgs>
        fields: Prisma.MediaTaskFieldRefs
        operations: {
          findUnique: {
            args: Prisma.MediaTaskFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.MediaTaskFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          findFirst: {
            args: Prisma.MediaTaskFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.MediaTaskFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          findMany: {
            args: Prisma.MediaTaskFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>[]
          }
          create: {
            args: Prisma.MediaTaskCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          createMany: {
            args: Prisma.MediaTaskCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.MediaTaskCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>[]
          }
          delete: {
            args: Prisma.MediaTaskDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          update: {
            args: Prisma.MediaTaskUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          deleteMany: {
            args: Prisma.MediaTaskDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.MediaTaskUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.MediaTaskUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>[]
          }
          upsert: {
            args: Prisma.MediaTaskUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MediaTaskPayload>
          }
          aggregate: {
            args: Prisma.MediaTaskAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateMediaTask>
          }
          groupBy: {
            args: Prisma.MediaTaskGroupByArgs<ExtArgs>
            result: $Utils.Optional<MediaTaskGroupByOutputType>[]
          }
          count: {
            args: Prisma.MediaTaskCountArgs<ExtArgs>
            result: $Utils.Optional<MediaTaskCountAggregateOutputType> | number
          }
        }
      }
      FileTask: {
        payload: Prisma.$FileTaskPayload<ExtArgs>
        fields: Prisma.FileTaskFieldRefs
        operations: {
          findUnique: {
            args: Prisma.FileTaskFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.FileTaskFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          findFirst: {
            args: Prisma.FileTaskFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.FileTaskFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          findMany: {
            args: Prisma.FileTaskFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>[]
          }
          create: {
            args: Prisma.FileTaskCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          createMany: {
            args: Prisma.FileTaskCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.FileTaskCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>[]
          }
          delete: {
            args: Prisma.FileTaskDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          update: {
            args: Prisma.FileTaskUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          deleteMany: {
            args: Prisma.FileTaskDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.FileTaskUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.FileTaskUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>[]
          }
          upsert: {
            args: Prisma.FileTaskUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FileTaskPayload>
          }
          aggregate: {
            args: Prisma.FileTaskAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateFileTask>
          }
          groupBy: {
            args: Prisma.FileTaskGroupByArgs<ExtArgs>
            result: $Utils.Optional<FileTaskGroupByOutputType>[]
          }
          count: {
            args: Prisma.FileTaskCountArgs<ExtArgs>
            result: $Utils.Optional<FileTaskCountAggregateOutputType> | number
          }
        }
      }
      TmdbSearchCache: {
        payload: Prisma.$TmdbSearchCachePayload<ExtArgs>
        fields: Prisma.TmdbSearchCacheFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TmdbSearchCacheFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TmdbSearchCacheFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          findFirst: {
            args: Prisma.TmdbSearchCacheFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TmdbSearchCacheFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          findMany: {
            args: Prisma.TmdbSearchCacheFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>[]
          }
          create: {
            args: Prisma.TmdbSearchCacheCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          createMany: {
            args: Prisma.TmdbSearchCacheCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TmdbSearchCacheCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>[]
          }
          delete: {
            args: Prisma.TmdbSearchCacheDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          update: {
            args: Prisma.TmdbSearchCacheUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          deleteMany: {
            args: Prisma.TmdbSearchCacheDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TmdbSearchCacheUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TmdbSearchCacheUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>[]
          }
          upsert: {
            args: Prisma.TmdbSearchCacheUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSearchCachePayload>
          }
          aggregate: {
            args: Prisma.TmdbSearchCacheAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTmdbSearchCache>
          }
          groupBy: {
            args: Prisma.TmdbSearchCacheGroupByArgs<ExtArgs>
            result: $Utils.Optional<TmdbSearchCacheGroupByOutputType>[]
          }
          count: {
            args: Prisma.TmdbSearchCacheCountArgs<ExtArgs>
            result: $Utils.Optional<TmdbSearchCacheCountAggregateOutputType> | number
          }
        }
      }
      TmdbMediaCache: {
        payload: Prisma.$TmdbMediaCachePayload<ExtArgs>
        fields: Prisma.TmdbMediaCacheFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TmdbMediaCacheFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TmdbMediaCacheFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          findFirst: {
            args: Prisma.TmdbMediaCacheFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TmdbMediaCacheFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          findMany: {
            args: Prisma.TmdbMediaCacheFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>[]
          }
          create: {
            args: Prisma.TmdbMediaCacheCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          createMany: {
            args: Prisma.TmdbMediaCacheCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TmdbMediaCacheCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>[]
          }
          delete: {
            args: Prisma.TmdbMediaCacheDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          update: {
            args: Prisma.TmdbMediaCacheUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          deleteMany: {
            args: Prisma.TmdbMediaCacheDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TmdbMediaCacheUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TmdbMediaCacheUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>[]
          }
          upsert: {
            args: Prisma.TmdbMediaCacheUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbMediaCachePayload>
          }
          aggregate: {
            args: Prisma.TmdbMediaCacheAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTmdbMediaCache>
          }
          groupBy: {
            args: Prisma.TmdbMediaCacheGroupByArgs<ExtArgs>
            result: $Utils.Optional<TmdbMediaCacheGroupByOutputType>[]
          }
          count: {
            args: Prisma.TmdbMediaCacheCountArgs<ExtArgs>
            result: $Utils.Optional<TmdbMediaCacheCountAggregateOutputType> | number
          }
        }
      }
      TmdbSeasonCache: {
        payload: Prisma.$TmdbSeasonCachePayload<ExtArgs>
        fields: Prisma.TmdbSeasonCacheFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TmdbSeasonCacheFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TmdbSeasonCacheFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          findFirst: {
            args: Prisma.TmdbSeasonCacheFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TmdbSeasonCacheFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          findMany: {
            args: Prisma.TmdbSeasonCacheFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>[]
          }
          create: {
            args: Prisma.TmdbSeasonCacheCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          createMany: {
            args: Prisma.TmdbSeasonCacheCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TmdbSeasonCacheCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>[]
          }
          delete: {
            args: Prisma.TmdbSeasonCacheDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          update: {
            args: Prisma.TmdbSeasonCacheUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          deleteMany: {
            args: Prisma.TmdbSeasonCacheDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TmdbSeasonCacheUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TmdbSeasonCacheUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>[]
          }
          upsert: {
            args: Prisma.TmdbSeasonCacheUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TmdbSeasonCachePayload>
          }
          aggregate: {
            args: Prisma.TmdbSeasonCacheAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTmdbSeasonCache>
          }
          groupBy: {
            args: Prisma.TmdbSeasonCacheGroupByArgs<ExtArgs>
            result: $Utils.Optional<TmdbSeasonCacheGroupByOutputType>[]
          }
          count: {
            args: Prisma.TmdbSeasonCacheCountArgs<ExtArgs>
            result: $Utils.Optional<TmdbSeasonCacheCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Shorthand for `emit: 'stdout'`
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events only
     * log: [
     *   { emit: 'event', level: 'query' },
     *   { emit: 'event', level: 'info' },
     *   { emit: 'event', level: 'warn' }
     *   { emit: 'event', level: 'error' }
     * ]
     * 
     * / Emit as events and log to stdout
     * og: [
     *  { emit: 'stdout', level: 'query' },
     *  { emit: 'stdout', level: 'info' },
     *  { emit: 'stdout', level: 'warn' }
     *  { emit: 'stdout', level: 'error' }
     * 
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    mediaTask?: MediaTaskOmit
    fileTask?: FileTaskOmit
    tmdbSearchCache?: TmdbSearchCacheOmit
    tmdbMediaCache?: TmdbMediaCacheOmit
    tmdbSeasonCache?: TmdbSeasonCacheOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

  export type GetLogType<T> = CheckIsLogLevel<
    T extends LogDefinition ? T['level'] : T
  >;

  export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
    ? GetLogType<T[number]>
    : never;

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type MediaTaskCountOutputType
   */

  export type MediaTaskCountOutputType = {
    fileTasks: number
  }

  export type MediaTaskCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    fileTasks?: boolean | MediaTaskCountOutputTypeCountFileTasksArgs
  }

  // Custom InputTypes
  /**
   * MediaTaskCountOutputType without action
   */
  export type MediaTaskCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTaskCountOutputType
     */
    select?: MediaTaskCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * MediaTaskCountOutputType without action
   */
  export type MediaTaskCountOutputTypeCountFileTasksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: FileTaskWhereInput
  }


  /**
   * Count Type TmdbMediaCacheCountOutputType
   */

  export type TmdbMediaCacheCountOutputType = {
    seasons: number
  }

  export type TmdbMediaCacheCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    seasons?: boolean | TmdbMediaCacheCountOutputTypeCountSeasonsArgs
  }

  // Custom InputTypes
  /**
   * TmdbMediaCacheCountOutputType without action
   */
  export type TmdbMediaCacheCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCacheCountOutputType
     */
    select?: TmdbMediaCacheCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * TmdbMediaCacheCountOutputType without action
   */
  export type TmdbMediaCacheCountOutputTypeCountSeasonsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TmdbSeasonCacheWhereInput
  }


  /**
   * Models
   */

  /**
   * Model MediaTask
   */

  export type AggregateMediaTask = {
    _count: MediaTaskCountAggregateOutputType | null
    _avg: MediaTaskAvgAggregateOutputType | null
    _sum: MediaTaskSumAggregateOutputType | null
    _min: MediaTaskMinAggregateOutputType | null
    _max: MediaTaskMaxAggregateOutputType | null
  }

  export type MediaTaskAvgAggregateOutputType = {
    tmdbId: number | null
    displayYear: number | null
  }

  export type MediaTaskSumAggregateOutputType = {
    tmdbId: number | null
    displayYear: number | null
  }

  export type MediaTaskMinAggregateOutputType = {
    id: string | null
    sourcePath: string | null
    status: $Enums.MediaTaskStatus | null
    type: $Enums.MediaType | null
    fileOperation: $Enums.FileOperation | null
    createdAt: Date | null
    finishedAt: Date | null
    tmdbId: number | null
    displayName: string | null
    displayYear: number | null
    posterPath: string | null
  }

  export type MediaTaskMaxAggregateOutputType = {
    id: string | null
    sourcePath: string | null
    status: $Enums.MediaTaskStatus | null
    type: $Enums.MediaType | null
    fileOperation: $Enums.FileOperation | null
    createdAt: Date | null
    finishedAt: Date | null
    tmdbId: number | null
    displayName: string | null
    displayYear: number | null
    posterPath: string | null
  }

  export type MediaTaskCountAggregateOutputType = {
    id: number
    sourcePath: number
    status: number
    type: number
    fileOperation: number
    createdAt: number
    finishedAt: number
    tmdbId: number
    displayName: number
    displayYear: number
    posterPath: number
    _all: number
  }


  export type MediaTaskAvgAggregateInputType = {
    tmdbId?: true
    displayYear?: true
  }

  export type MediaTaskSumAggregateInputType = {
    tmdbId?: true
    displayYear?: true
  }

  export type MediaTaskMinAggregateInputType = {
    id?: true
    sourcePath?: true
    status?: true
    type?: true
    fileOperation?: true
    createdAt?: true
    finishedAt?: true
    tmdbId?: true
    displayName?: true
    displayYear?: true
    posterPath?: true
  }

  export type MediaTaskMaxAggregateInputType = {
    id?: true
    sourcePath?: true
    status?: true
    type?: true
    fileOperation?: true
    createdAt?: true
    finishedAt?: true
    tmdbId?: true
    displayName?: true
    displayYear?: true
    posterPath?: true
  }

  export type MediaTaskCountAggregateInputType = {
    id?: true
    sourcePath?: true
    status?: true
    type?: true
    fileOperation?: true
    createdAt?: true
    finishedAt?: true
    tmdbId?: true
    displayName?: true
    displayYear?: true
    posterPath?: true
    _all?: true
  }

  export type MediaTaskAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which MediaTask to aggregate.
     */
    where?: MediaTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MediaTasks to fetch.
     */
    orderBy?: MediaTaskOrderByWithRelationInput | MediaTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: MediaTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MediaTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MediaTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned MediaTasks
    **/
    _count?: true | MediaTaskCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: MediaTaskAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: MediaTaskSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: MediaTaskMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: MediaTaskMaxAggregateInputType
  }

  export type GetMediaTaskAggregateType<T extends MediaTaskAggregateArgs> = {
        [P in keyof T & keyof AggregateMediaTask]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateMediaTask[P]>
      : GetScalarType<T[P], AggregateMediaTask[P]>
  }




  export type MediaTaskGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: MediaTaskWhereInput
    orderBy?: MediaTaskOrderByWithAggregationInput | MediaTaskOrderByWithAggregationInput[]
    by: MediaTaskScalarFieldEnum[] | MediaTaskScalarFieldEnum
    having?: MediaTaskScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: MediaTaskCountAggregateInputType | true
    _avg?: MediaTaskAvgAggregateInputType
    _sum?: MediaTaskSumAggregateInputType
    _min?: MediaTaskMinAggregateInputType
    _max?: MediaTaskMaxAggregateInputType
  }

  export type MediaTaskGroupByOutputType = {
    id: string
    sourcePath: string
    status: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt: Date
    finishedAt: Date | null
    tmdbId: number | null
    displayName: string | null
    displayYear: number | null
    posterPath: string | null
    _count: MediaTaskCountAggregateOutputType | null
    _avg: MediaTaskAvgAggregateOutputType | null
    _sum: MediaTaskSumAggregateOutputType | null
    _min: MediaTaskMinAggregateOutputType | null
    _max: MediaTaskMaxAggregateOutputType | null
  }

  type GetMediaTaskGroupByPayload<T extends MediaTaskGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<MediaTaskGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof MediaTaskGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], MediaTaskGroupByOutputType[P]>
            : GetScalarType<T[P], MediaTaskGroupByOutputType[P]>
        }
      >
    >


  export type MediaTaskSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    status?: boolean
    type?: boolean
    fileOperation?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    tmdbId?: boolean
    displayName?: boolean
    displayYear?: boolean
    posterPath?: boolean
    fileTasks?: boolean | MediaTask$fileTasksArgs<ExtArgs>
    _count?: boolean | MediaTaskCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["mediaTask"]>

  export type MediaTaskSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    status?: boolean
    type?: boolean
    fileOperation?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    tmdbId?: boolean
    displayName?: boolean
    displayYear?: boolean
    posterPath?: boolean
  }, ExtArgs["result"]["mediaTask"]>

  export type MediaTaskSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    status?: boolean
    type?: boolean
    fileOperation?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    tmdbId?: boolean
    displayName?: boolean
    displayYear?: boolean
    posterPath?: boolean
  }, ExtArgs["result"]["mediaTask"]>

  export type MediaTaskSelectScalar = {
    id?: boolean
    sourcePath?: boolean
    status?: boolean
    type?: boolean
    fileOperation?: boolean
    createdAt?: boolean
    finishedAt?: boolean
    tmdbId?: boolean
    displayName?: boolean
    displayYear?: boolean
    posterPath?: boolean
  }

  export type MediaTaskOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sourcePath" | "status" | "type" | "fileOperation" | "createdAt" | "finishedAt" | "tmdbId" | "displayName" | "displayYear" | "posterPath", ExtArgs["result"]["mediaTask"]>
  export type MediaTaskInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    fileTasks?: boolean | MediaTask$fileTasksArgs<ExtArgs>
    _count?: boolean | MediaTaskCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type MediaTaskIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type MediaTaskIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $MediaTaskPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "MediaTask"
    objects: {
      fileTasks: Prisma.$FileTaskPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      sourcePath: string
      status: $Enums.MediaTaskStatus
      type: $Enums.MediaType
      fileOperation: $Enums.FileOperation
      createdAt: Date
      finishedAt: Date | null
      tmdbId: number | null
      displayName: string | null
      displayYear: number | null
      posterPath: string | null
    }, ExtArgs["result"]["mediaTask"]>
    composites: {}
  }

  type MediaTaskGetPayload<S extends boolean | null | undefined | MediaTaskDefaultArgs> = $Result.GetResult<Prisma.$MediaTaskPayload, S>

  type MediaTaskCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<MediaTaskFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: MediaTaskCountAggregateInputType | true
    }

  export interface MediaTaskDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['MediaTask'], meta: { name: 'MediaTask' } }
    /**
     * Find zero or one MediaTask that matches the filter.
     * @param {MediaTaskFindUniqueArgs} args - Arguments to find a MediaTask
     * @example
     * // Get one MediaTask
     * const mediaTask = await prisma.mediaTask.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends MediaTaskFindUniqueArgs>(args: SelectSubset<T, MediaTaskFindUniqueArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one MediaTask that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {MediaTaskFindUniqueOrThrowArgs} args - Arguments to find a MediaTask
     * @example
     * // Get one MediaTask
     * const mediaTask = await prisma.mediaTask.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends MediaTaskFindUniqueOrThrowArgs>(args: SelectSubset<T, MediaTaskFindUniqueOrThrowArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first MediaTask that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskFindFirstArgs} args - Arguments to find a MediaTask
     * @example
     * // Get one MediaTask
     * const mediaTask = await prisma.mediaTask.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends MediaTaskFindFirstArgs>(args?: SelectSubset<T, MediaTaskFindFirstArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first MediaTask that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskFindFirstOrThrowArgs} args - Arguments to find a MediaTask
     * @example
     * // Get one MediaTask
     * const mediaTask = await prisma.mediaTask.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends MediaTaskFindFirstOrThrowArgs>(args?: SelectSubset<T, MediaTaskFindFirstOrThrowArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more MediaTasks that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all MediaTasks
     * const mediaTasks = await prisma.mediaTask.findMany()
     * 
     * // Get first 10 MediaTasks
     * const mediaTasks = await prisma.mediaTask.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const mediaTaskWithIdOnly = await prisma.mediaTask.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends MediaTaskFindManyArgs>(args?: SelectSubset<T, MediaTaskFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a MediaTask.
     * @param {MediaTaskCreateArgs} args - Arguments to create a MediaTask.
     * @example
     * // Create one MediaTask
     * const MediaTask = await prisma.mediaTask.create({
     *   data: {
     *     // ... data to create a MediaTask
     *   }
     * })
     * 
     */
    create<T extends MediaTaskCreateArgs>(args: SelectSubset<T, MediaTaskCreateArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many MediaTasks.
     * @param {MediaTaskCreateManyArgs} args - Arguments to create many MediaTasks.
     * @example
     * // Create many MediaTasks
     * const mediaTask = await prisma.mediaTask.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends MediaTaskCreateManyArgs>(args?: SelectSubset<T, MediaTaskCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many MediaTasks and returns the data saved in the database.
     * @param {MediaTaskCreateManyAndReturnArgs} args - Arguments to create many MediaTasks.
     * @example
     * // Create many MediaTasks
     * const mediaTask = await prisma.mediaTask.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many MediaTasks and only return the `id`
     * const mediaTaskWithIdOnly = await prisma.mediaTask.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends MediaTaskCreateManyAndReturnArgs>(args?: SelectSubset<T, MediaTaskCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a MediaTask.
     * @param {MediaTaskDeleteArgs} args - Arguments to delete one MediaTask.
     * @example
     * // Delete one MediaTask
     * const MediaTask = await prisma.mediaTask.delete({
     *   where: {
     *     // ... filter to delete one MediaTask
     *   }
     * })
     * 
     */
    delete<T extends MediaTaskDeleteArgs>(args: SelectSubset<T, MediaTaskDeleteArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one MediaTask.
     * @param {MediaTaskUpdateArgs} args - Arguments to update one MediaTask.
     * @example
     * // Update one MediaTask
     * const mediaTask = await prisma.mediaTask.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends MediaTaskUpdateArgs>(args: SelectSubset<T, MediaTaskUpdateArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more MediaTasks.
     * @param {MediaTaskDeleteManyArgs} args - Arguments to filter MediaTasks to delete.
     * @example
     * // Delete a few MediaTasks
     * const { count } = await prisma.mediaTask.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends MediaTaskDeleteManyArgs>(args?: SelectSubset<T, MediaTaskDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more MediaTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many MediaTasks
     * const mediaTask = await prisma.mediaTask.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends MediaTaskUpdateManyArgs>(args: SelectSubset<T, MediaTaskUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more MediaTasks and returns the data updated in the database.
     * @param {MediaTaskUpdateManyAndReturnArgs} args - Arguments to update many MediaTasks.
     * @example
     * // Update many MediaTasks
     * const mediaTask = await prisma.mediaTask.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more MediaTasks and only return the `id`
     * const mediaTaskWithIdOnly = await prisma.mediaTask.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends MediaTaskUpdateManyAndReturnArgs>(args: SelectSubset<T, MediaTaskUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one MediaTask.
     * @param {MediaTaskUpsertArgs} args - Arguments to update or create a MediaTask.
     * @example
     * // Update or create a MediaTask
     * const mediaTask = await prisma.mediaTask.upsert({
     *   create: {
     *     // ... data to create a MediaTask
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the MediaTask we want to update
     *   }
     * })
     */
    upsert<T extends MediaTaskUpsertArgs>(args: SelectSubset<T, MediaTaskUpsertArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of MediaTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskCountArgs} args - Arguments to filter MediaTasks to count.
     * @example
     * // Count the number of MediaTasks
     * const count = await prisma.mediaTask.count({
     *   where: {
     *     // ... the filter for the MediaTasks we want to count
     *   }
     * })
    **/
    count<T extends MediaTaskCountArgs>(
      args?: Subset<T, MediaTaskCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], MediaTaskCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a MediaTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends MediaTaskAggregateArgs>(args: Subset<T, MediaTaskAggregateArgs>): Prisma.PrismaPromise<GetMediaTaskAggregateType<T>>

    /**
     * Group by MediaTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MediaTaskGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends MediaTaskGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: MediaTaskGroupByArgs['orderBy'] }
        : { orderBy?: MediaTaskGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, MediaTaskGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMediaTaskGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the MediaTask model
   */
  readonly fields: MediaTaskFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for MediaTask.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__MediaTaskClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    fileTasks<T extends MediaTask$fileTasksArgs<ExtArgs> = {}>(args?: Subset<T, MediaTask$fileTasksArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the MediaTask model
   */
  interface MediaTaskFieldRefs {
    readonly id: FieldRef<"MediaTask", 'String'>
    readonly sourcePath: FieldRef<"MediaTask", 'String'>
    readonly status: FieldRef<"MediaTask", 'MediaTaskStatus'>
    readonly type: FieldRef<"MediaTask", 'MediaType'>
    readonly fileOperation: FieldRef<"MediaTask", 'FileOperation'>
    readonly createdAt: FieldRef<"MediaTask", 'DateTime'>
    readonly finishedAt: FieldRef<"MediaTask", 'DateTime'>
    readonly tmdbId: FieldRef<"MediaTask", 'Int'>
    readonly displayName: FieldRef<"MediaTask", 'String'>
    readonly displayYear: FieldRef<"MediaTask", 'Int'>
    readonly posterPath: FieldRef<"MediaTask", 'String'>
  }
    

  // Custom InputTypes
  /**
   * MediaTask findUnique
   */
  export type MediaTaskFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter, which MediaTask to fetch.
     */
    where: MediaTaskWhereUniqueInput
  }

  /**
   * MediaTask findUniqueOrThrow
   */
  export type MediaTaskFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter, which MediaTask to fetch.
     */
    where: MediaTaskWhereUniqueInput
  }

  /**
   * MediaTask findFirst
   */
  export type MediaTaskFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter, which MediaTask to fetch.
     */
    where?: MediaTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MediaTasks to fetch.
     */
    orderBy?: MediaTaskOrderByWithRelationInput | MediaTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for MediaTasks.
     */
    cursor?: MediaTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MediaTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MediaTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of MediaTasks.
     */
    distinct?: MediaTaskScalarFieldEnum | MediaTaskScalarFieldEnum[]
  }

  /**
   * MediaTask findFirstOrThrow
   */
  export type MediaTaskFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter, which MediaTask to fetch.
     */
    where?: MediaTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MediaTasks to fetch.
     */
    orderBy?: MediaTaskOrderByWithRelationInput | MediaTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for MediaTasks.
     */
    cursor?: MediaTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MediaTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MediaTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of MediaTasks.
     */
    distinct?: MediaTaskScalarFieldEnum | MediaTaskScalarFieldEnum[]
  }

  /**
   * MediaTask findMany
   */
  export type MediaTaskFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter, which MediaTasks to fetch.
     */
    where?: MediaTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of MediaTasks to fetch.
     */
    orderBy?: MediaTaskOrderByWithRelationInput | MediaTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing MediaTasks.
     */
    cursor?: MediaTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` MediaTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` MediaTasks.
     */
    skip?: number
    distinct?: MediaTaskScalarFieldEnum | MediaTaskScalarFieldEnum[]
  }

  /**
   * MediaTask create
   */
  export type MediaTaskCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * The data needed to create a MediaTask.
     */
    data: XOR<MediaTaskCreateInput, MediaTaskUncheckedCreateInput>
  }

  /**
   * MediaTask createMany
   */
  export type MediaTaskCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many MediaTasks.
     */
    data: MediaTaskCreateManyInput | MediaTaskCreateManyInput[]
  }

  /**
   * MediaTask createManyAndReturn
   */
  export type MediaTaskCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * The data used to create many MediaTasks.
     */
    data: MediaTaskCreateManyInput | MediaTaskCreateManyInput[]
  }

  /**
   * MediaTask update
   */
  export type MediaTaskUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * The data needed to update a MediaTask.
     */
    data: XOR<MediaTaskUpdateInput, MediaTaskUncheckedUpdateInput>
    /**
     * Choose, which MediaTask to update.
     */
    where: MediaTaskWhereUniqueInput
  }

  /**
   * MediaTask updateMany
   */
  export type MediaTaskUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update MediaTasks.
     */
    data: XOR<MediaTaskUpdateManyMutationInput, MediaTaskUncheckedUpdateManyInput>
    /**
     * Filter which MediaTasks to update
     */
    where?: MediaTaskWhereInput
    /**
     * Limit how many MediaTasks to update.
     */
    limit?: number
  }

  /**
   * MediaTask updateManyAndReturn
   */
  export type MediaTaskUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * The data used to update MediaTasks.
     */
    data: XOR<MediaTaskUpdateManyMutationInput, MediaTaskUncheckedUpdateManyInput>
    /**
     * Filter which MediaTasks to update
     */
    where?: MediaTaskWhereInput
    /**
     * Limit how many MediaTasks to update.
     */
    limit?: number
  }

  /**
   * MediaTask upsert
   */
  export type MediaTaskUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * The filter to search for the MediaTask to update in case it exists.
     */
    where: MediaTaskWhereUniqueInput
    /**
     * In case the MediaTask found by the `where` argument doesn't exist, create a new MediaTask with this data.
     */
    create: XOR<MediaTaskCreateInput, MediaTaskUncheckedCreateInput>
    /**
     * In case the MediaTask was found with the provided `where` argument, update it with this data.
     */
    update: XOR<MediaTaskUpdateInput, MediaTaskUncheckedUpdateInput>
  }

  /**
   * MediaTask delete
   */
  export type MediaTaskDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
    /**
     * Filter which MediaTask to delete.
     */
    where: MediaTaskWhereUniqueInput
  }

  /**
   * MediaTask deleteMany
   */
  export type MediaTaskDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which MediaTasks to delete
     */
    where?: MediaTaskWhereInput
    /**
     * Limit how many MediaTasks to delete.
     */
    limit?: number
  }

  /**
   * MediaTask.fileTasks
   */
  export type MediaTask$fileTasksArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    where?: FileTaskWhereInput
    orderBy?: FileTaskOrderByWithRelationInput | FileTaskOrderByWithRelationInput[]
    cursor?: FileTaskWhereUniqueInput
    take?: number
    skip?: number
    distinct?: FileTaskScalarFieldEnum | FileTaskScalarFieldEnum[]
  }

  /**
   * MediaTask without action
   */
  export type MediaTaskDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MediaTask
     */
    select?: MediaTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the MediaTask
     */
    omit?: MediaTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MediaTaskInclude<ExtArgs> | null
  }


  /**
   * Model FileTask
   */

  export type AggregateFileTask = {
    _count: FileTaskCountAggregateOutputType | null
    _avg: FileTaskAvgAggregateOutputType | null
    _sum: FileTaskSumAggregateOutputType | null
    _min: FileTaskMinAggregateOutputType | null
    _max: FileTaskMaxAggregateOutputType | null
  }

  export type FileTaskAvgAggregateOutputType = {
    fileSize: number | null
    duration: number | null
    targetSeason: number | null
    targetEpisode: number | null
  }

  export type FileTaskSumAggregateOutputType = {
    fileSize: bigint | null
    duration: number | null
    targetSeason: number | null
    targetEpisode: number | null
  }

  export type FileTaskMinAggregateOutputType = {
    id: string | null
    sourcePath: string | null
    fileOperation: $Enums.FileOperation | null
    fileSize: bigint | null
    duration: number | null
    resolution: string | null
    fileType: $Enums.FileType | null
    status: $Enums.FileTaskStatus | null
    targetSeason: number | null
    targetEpisode: number | null
    targetPath: string | null
    mediaTaskId: string | null
  }

  export type FileTaskMaxAggregateOutputType = {
    id: string | null
    sourcePath: string | null
    fileOperation: $Enums.FileOperation | null
    fileSize: bigint | null
    duration: number | null
    resolution: string | null
    fileType: $Enums.FileType | null
    status: $Enums.FileTaskStatus | null
    targetSeason: number | null
    targetEpisode: number | null
    targetPath: string | null
    mediaTaskId: string | null
  }

  export type FileTaskCountAggregateOutputType = {
    id: number
    sourcePath: number
    associatedFiles: number
    fileOperation: number
    fileSize: number
    duration: number
    resolution: number
    fileType: number
    status: number
    targetSeason: number
    targetEpisode: number
    targetPath: number
    errorMessage: number
    mediaTaskId: number
    _all: number
  }


  export type FileTaskAvgAggregateInputType = {
    fileSize?: true
    duration?: true
    targetSeason?: true
    targetEpisode?: true
  }

  export type FileTaskSumAggregateInputType = {
    fileSize?: true
    duration?: true
    targetSeason?: true
    targetEpisode?: true
  }

  export type FileTaskMinAggregateInputType = {
    id?: true
    sourcePath?: true
    fileOperation?: true
    fileSize?: true
    duration?: true
    resolution?: true
    fileType?: true
    status?: true
    targetSeason?: true
    targetEpisode?: true
    targetPath?: true
    mediaTaskId?: true
  }

  export type FileTaskMaxAggregateInputType = {
    id?: true
    sourcePath?: true
    fileOperation?: true
    fileSize?: true
    duration?: true
    resolution?: true
    fileType?: true
    status?: true
    targetSeason?: true
    targetEpisode?: true
    targetPath?: true
    mediaTaskId?: true
  }

  export type FileTaskCountAggregateInputType = {
    id?: true
    sourcePath?: true
    associatedFiles?: true
    fileOperation?: true
    fileSize?: true
    duration?: true
    resolution?: true
    fileType?: true
    status?: true
    targetSeason?: true
    targetEpisode?: true
    targetPath?: true
    errorMessage?: true
    mediaTaskId?: true
    _all?: true
  }

  export type FileTaskAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which FileTask to aggregate.
     */
    where?: FileTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FileTasks to fetch.
     */
    orderBy?: FileTaskOrderByWithRelationInput | FileTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: FileTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FileTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FileTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned FileTasks
    **/
    _count?: true | FileTaskCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: FileTaskAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: FileTaskSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: FileTaskMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: FileTaskMaxAggregateInputType
  }

  export type GetFileTaskAggregateType<T extends FileTaskAggregateArgs> = {
        [P in keyof T & keyof AggregateFileTask]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateFileTask[P]>
      : GetScalarType<T[P], AggregateFileTask[P]>
  }




  export type FileTaskGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: FileTaskWhereInput
    orderBy?: FileTaskOrderByWithAggregationInput | FileTaskOrderByWithAggregationInput[]
    by: FileTaskScalarFieldEnum[] | FileTaskScalarFieldEnum
    having?: FileTaskScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: FileTaskCountAggregateInputType | true
    _avg?: FileTaskAvgAggregateInputType
    _sum?: FileTaskSumAggregateInputType
    _min?: FileTaskMinAggregateInputType
    _max?: FileTaskMaxAggregateInputType
  }

  export type FileTaskGroupByOutputType = {
    id: string
    sourcePath: string
    associatedFiles: JsonValue | null
    fileOperation: $Enums.FileOperation
    fileSize: bigint | null
    duration: number | null
    resolution: string | null
    fileType: $Enums.FileType
    status: $Enums.FileTaskStatus
    targetSeason: number | null
    targetEpisode: number | null
    targetPath: string | null
    errorMessage: JsonValue | null
    mediaTaskId: string
    _count: FileTaskCountAggregateOutputType | null
    _avg: FileTaskAvgAggregateOutputType | null
    _sum: FileTaskSumAggregateOutputType | null
    _min: FileTaskMinAggregateOutputType | null
    _max: FileTaskMaxAggregateOutputType | null
  }

  type GetFileTaskGroupByPayload<T extends FileTaskGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<FileTaskGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof FileTaskGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], FileTaskGroupByOutputType[P]>
            : GetScalarType<T[P], FileTaskGroupByOutputType[P]>
        }
      >
    >


  export type FileTaskSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    associatedFiles?: boolean
    fileOperation?: boolean
    fileSize?: boolean
    duration?: boolean
    resolution?: boolean
    fileType?: boolean
    status?: boolean
    targetSeason?: boolean
    targetEpisode?: boolean
    targetPath?: boolean
    errorMessage?: boolean
    mediaTaskId?: boolean
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["fileTask"]>

  export type FileTaskSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    associatedFiles?: boolean
    fileOperation?: boolean
    fileSize?: boolean
    duration?: boolean
    resolution?: boolean
    fileType?: boolean
    status?: boolean
    targetSeason?: boolean
    targetEpisode?: boolean
    targetPath?: boolean
    errorMessage?: boolean
    mediaTaskId?: boolean
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["fileTask"]>

  export type FileTaskSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    sourcePath?: boolean
    associatedFiles?: boolean
    fileOperation?: boolean
    fileSize?: boolean
    duration?: boolean
    resolution?: boolean
    fileType?: boolean
    status?: boolean
    targetSeason?: boolean
    targetEpisode?: boolean
    targetPath?: boolean
    errorMessage?: boolean
    mediaTaskId?: boolean
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["fileTask"]>

  export type FileTaskSelectScalar = {
    id?: boolean
    sourcePath?: boolean
    associatedFiles?: boolean
    fileOperation?: boolean
    fileSize?: boolean
    duration?: boolean
    resolution?: boolean
    fileType?: boolean
    status?: boolean
    targetSeason?: boolean
    targetEpisode?: boolean
    targetPath?: boolean
    errorMessage?: boolean
    mediaTaskId?: boolean
  }

  export type FileTaskOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "sourcePath" | "associatedFiles" | "fileOperation" | "fileSize" | "duration" | "resolution" | "fileType" | "status" | "targetSeason" | "targetEpisode" | "targetPath" | "errorMessage" | "mediaTaskId", ExtArgs["result"]["fileTask"]>
  export type FileTaskInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }
  export type FileTaskIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }
  export type FileTaskIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    mediaTask?: boolean | MediaTaskDefaultArgs<ExtArgs>
  }

  export type $FileTaskPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "FileTask"
    objects: {
      mediaTask: Prisma.$MediaTaskPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      sourcePath: string
      associatedFiles: Prisma.JsonValue | null
      fileOperation: $Enums.FileOperation
      fileSize: bigint | null
      duration: number | null
      resolution: string | null
      fileType: $Enums.FileType
      status: $Enums.FileTaskStatus
      targetSeason: number | null
      targetEpisode: number | null
      targetPath: string | null
      errorMessage: Prisma.JsonValue | null
      mediaTaskId: string
    }, ExtArgs["result"]["fileTask"]>
    composites: {}
  }

  type FileTaskGetPayload<S extends boolean | null | undefined | FileTaskDefaultArgs> = $Result.GetResult<Prisma.$FileTaskPayload, S>

  type FileTaskCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<FileTaskFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: FileTaskCountAggregateInputType | true
    }

  export interface FileTaskDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['FileTask'], meta: { name: 'FileTask' } }
    /**
     * Find zero or one FileTask that matches the filter.
     * @param {FileTaskFindUniqueArgs} args - Arguments to find a FileTask
     * @example
     * // Get one FileTask
     * const fileTask = await prisma.fileTask.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends FileTaskFindUniqueArgs>(args: SelectSubset<T, FileTaskFindUniqueArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one FileTask that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {FileTaskFindUniqueOrThrowArgs} args - Arguments to find a FileTask
     * @example
     * // Get one FileTask
     * const fileTask = await prisma.fileTask.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends FileTaskFindUniqueOrThrowArgs>(args: SelectSubset<T, FileTaskFindUniqueOrThrowArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first FileTask that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskFindFirstArgs} args - Arguments to find a FileTask
     * @example
     * // Get one FileTask
     * const fileTask = await prisma.fileTask.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends FileTaskFindFirstArgs>(args?: SelectSubset<T, FileTaskFindFirstArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first FileTask that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskFindFirstOrThrowArgs} args - Arguments to find a FileTask
     * @example
     * // Get one FileTask
     * const fileTask = await prisma.fileTask.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends FileTaskFindFirstOrThrowArgs>(args?: SelectSubset<T, FileTaskFindFirstOrThrowArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more FileTasks that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all FileTasks
     * const fileTasks = await prisma.fileTask.findMany()
     * 
     * // Get first 10 FileTasks
     * const fileTasks = await prisma.fileTask.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const fileTaskWithIdOnly = await prisma.fileTask.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends FileTaskFindManyArgs>(args?: SelectSubset<T, FileTaskFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a FileTask.
     * @param {FileTaskCreateArgs} args - Arguments to create a FileTask.
     * @example
     * // Create one FileTask
     * const FileTask = await prisma.fileTask.create({
     *   data: {
     *     // ... data to create a FileTask
     *   }
     * })
     * 
     */
    create<T extends FileTaskCreateArgs>(args: SelectSubset<T, FileTaskCreateArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many FileTasks.
     * @param {FileTaskCreateManyArgs} args - Arguments to create many FileTasks.
     * @example
     * // Create many FileTasks
     * const fileTask = await prisma.fileTask.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends FileTaskCreateManyArgs>(args?: SelectSubset<T, FileTaskCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many FileTasks and returns the data saved in the database.
     * @param {FileTaskCreateManyAndReturnArgs} args - Arguments to create many FileTasks.
     * @example
     * // Create many FileTasks
     * const fileTask = await prisma.fileTask.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many FileTasks and only return the `id`
     * const fileTaskWithIdOnly = await prisma.fileTask.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends FileTaskCreateManyAndReturnArgs>(args?: SelectSubset<T, FileTaskCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a FileTask.
     * @param {FileTaskDeleteArgs} args - Arguments to delete one FileTask.
     * @example
     * // Delete one FileTask
     * const FileTask = await prisma.fileTask.delete({
     *   where: {
     *     // ... filter to delete one FileTask
     *   }
     * })
     * 
     */
    delete<T extends FileTaskDeleteArgs>(args: SelectSubset<T, FileTaskDeleteArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one FileTask.
     * @param {FileTaskUpdateArgs} args - Arguments to update one FileTask.
     * @example
     * // Update one FileTask
     * const fileTask = await prisma.fileTask.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends FileTaskUpdateArgs>(args: SelectSubset<T, FileTaskUpdateArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more FileTasks.
     * @param {FileTaskDeleteManyArgs} args - Arguments to filter FileTasks to delete.
     * @example
     * // Delete a few FileTasks
     * const { count } = await prisma.fileTask.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends FileTaskDeleteManyArgs>(args?: SelectSubset<T, FileTaskDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more FileTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many FileTasks
     * const fileTask = await prisma.fileTask.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends FileTaskUpdateManyArgs>(args: SelectSubset<T, FileTaskUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more FileTasks and returns the data updated in the database.
     * @param {FileTaskUpdateManyAndReturnArgs} args - Arguments to update many FileTasks.
     * @example
     * // Update many FileTasks
     * const fileTask = await prisma.fileTask.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more FileTasks and only return the `id`
     * const fileTaskWithIdOnly = await prisma.fileTask.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends FileTaskUpdateManyAndReturnArgs>(args: SelectSubset<T, FileTaskUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one FileTask.
     * @param {FileTaskUpsertArgs} args - Arguments to update or create a FileTask.
     * @example
     * // Update or create a FileTask
     * const fileTask = await prisma.fileTask.upsert({
     *   create: {
     *     // ... data to create a FileTask
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the FileTask we want to update
     *   }
     * })
     */
    upsert<T extends FileTaskUpsertArgs>(args: SelectSubset<T, FileTaskUpsertArgs<ExtArgs>>): Prisma__FileTaskClient<$Result.GetResult<Prisma.$FileTaskPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of FileTasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskCountArgs} args - Arguments to filter FileTasks to count.
     * @example
     * // Count the number of FileTasks
     * const count = await prisma.fileTask.count({
     *   where: {
     *     // ... the filter for the FileTasks we want to count
     *   }
     * })
    **/
    count<T extends FileTaskCountArgs>(
      args?: Subset<T, FileTaskCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], FileTaskCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a FileTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends FileTaskAggregateArgs>(args: Subset<T, FileTaskAggregateArgs>): Prisma.PrismaPromise<GetFileTaskAggregateType<T>>

    /**
     * Group by FileTask.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FileTaskGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends FileTaskGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: FileTaskGroupByArgs['orderBy'] }
        : { orderBy?: FileTaskGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, FileTaskGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetFileTaskGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the FileTask model
   */
  readonly fields: FileTaskFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for FileTask.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__FileTaskClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    mediaTask<T extends MediaTaskDefaultArgs<ExtArgs> = {}>(args?: Subset<T, MediaTaskDefaultArgs<ExtArgs>>): Prisma__MediaTaskClient<$Result.GetResult<Prisma.$MediaTaskPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the FileTask model
   */
  interface FileTaskFieldRefs {
    readonly id: FieldRef<"FileTask", 'String'>
    readonly sourcePath: FieldRef<"FileTask", 'String'>
    readonly associatedFiles: FieldRef<"FileTask", 'Json'>
    readonly fileOperation: FieldRef<"FileTask", 'FileOperation'>
    readonly fileSize: FieldRef<"FileTask", 'BigInt'>
    readonly duration: FieldRef<"FileTask", 'Float'>
    readonly resolution: FieldRef<"FileTask", 'String'>
    readonly fileType: FieldRef<"FileTask", 'FileType'>
    readonly status: FieldRef<"FileTask", 'FileTaskStatus'>
    readonly targetSeason: FieldRef<"FileTask", 'Int'>
    readonly targetEpisode: FieldRef<"FileTask", 'Int'>
    readonly targetPath: FieldRef<"FileTask", 'String'>
    readonly errorMessage: FieldRef<"FileTask", 'Json'>
    readonly mediaTaskId: FieldRef<"FileTask", 'String'>
  }
    

  // Custom InputTypes
  /**
   * FileTask findUnique
   */
  export type FileTaskFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter, which FileTask to fetch.
     */
    where: FileTaskWhereUniqueInput
  }

  /**
   * FileTask findUniqueOrThrow
   */
  export type FileTaskFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter, which FileTask to fetch.
     */
    where: FileTaskWhereUniqueInput
  }

  /**
   * FileTask findFirst
   */
  export type FileTaskFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter, which FileTask to fetch.
     */
    where?: FileTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FileTasks to fetch.
     */
    orderBy?: FileTaskOrderByWithRelationInput | FileTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for FileTasks.
     */
    cursor?: FileTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FileTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FileTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of FileTasks.
     */
    distinct?: FileTaskScalarFieldEnum | FileTaskScalarFieldEnum[]
  }

  /**
   * FileTask findFirstOrThrow
   */
  export type FileTaskFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter, which FileTask to fetch.
     */
    where?: FileTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FileTasks to fetch.
     */
    orderBy?: FileTaskOrderByWithRelationInput | FileTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for FileTasks.
     */
    cursor?: FileTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FileTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FileTasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of FileTasks.
     */
    distinct?: FileTaskScalarFieldEnum | FileTaskScalarFieldEnum[]
  }

  /**
   * FileTask findMany
   */
  export type FileTaskFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter, which FileTasks to fetch.
     */
    where?: FileTaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FileTasks to fetch.
     */
    orderBy?: FileTaskOrderByWithRelationInput | FileTaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing FileTasks.
     */
    cursor?: FileTaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FileTasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FileTasks.
     */
    skip?: number
    distinct?: FileTaskScalarFieldEnum | FileTaskScalarFieldEnum[]
  }

  /**
   * FileTask create
   */
  export type FileTaskCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * The data needed to create a FileTask.
     */
    data: XOR<FileTaskCreateInput, FileTaskUncheckedCreateInput>
  }

  /**
   * FileTask createMany
   */
  export type FileTaskCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many FileTasks.
     */
    data: FileTaskCreateManyInput | FileTaskCreateManyInput[]
  }

  /**
   * FileTask createManyAndReturn
   */
  export type FileTaskCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * The data used to create many FileTasks.
     */
    data: FileTaskCreateManyInput | FileTaskCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * FileTask update
   */
  export type FileTaskUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * The data needed to update a FileTask.
     */
    data: XOR<FileTaskUpdateInput, FileTaskUncheckedUpdateInput>
    /**
     * Choose, which FileTask to update.
     */
    where: FileTaskWhereUniqueInput
  }

  /**
   * FileTask updateMany
   */
  export type FileTaskUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update FileTasks.
     */
    data: XOR<FileTaskUpdateManyMutationInput, FileTaskUncheckedUpdateManyInput>
    /**
     * Filter which FileTasks to update
     */
    where?: FileTaskWhereInput
    /**
     * Limit how many FileTasks to update.
     */
    limit?: number
  }

  /**
   * FileTask updateManyAndReturn
   */
  export type FileTaskUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * The data used to update FileTasks.
     */
    data: XOR<FileTaskUpdateManyMutationInput, FileTaskUncheckedUpdateManyInput>
    /**
     * Filter which FileTasks to update
     */
    where?: FileTaskWhereInput
    /**
     * Limit how many FileTasks to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * FileTask upsert
   */
  export type FileTaskUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * The filter to search for the FileTask to update in case it exists.
     */
    where: FileTaskWhereUniqueInput
    /**
     * In case the FileTask found by the `where` argument doesn't exist, create a new FileTask with this data.
     */
    create: XOR<FileTaskCreateInput, FileTaskUncheckedCreateInput>
    /**
     * In case the FileTask was found with the provided `where` argument, update it with this data.
     */
    update: XOR<FileTaskUpdateInput, FileTaskUncheckedUpdateInput>
  }

  /**
   * FileTask delete
   */
  export type FileTaskDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
    /**
     * Filter which FileTask to delete.
     */
    where: FileTaskWhereUniqueInput
  }

  /**
   * FileTask deleteMany
   */
  export type FileTaskDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which FileTasks to delete
     */
    where?: FileTaskWhereInput
    /**
     * Limit how many FileTasks to delete.
     */
    limit?: number
  }

  /**
   * FileTask without action
   */
  export type FileTaskDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FileTask
     */
    select?: FileTaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the FileTask
     */
    omit?: FileTaskOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FileTaskInclude<ExtArgs> | null
  }


  /**
   * Model TmdbSearchCache
   */

  export type AggregateTmdbSearchCache = {
    _count: TmdbSearchCacheCountAggregateOutputType | null
    _min: TmdbSearchCacheMinAggregateOutputType | null
    _max: TmdbSearchCacheMaxAggregateOutputType | null
  }

  export type TmdbSearchCacheMinAggregateOutputType = {
    query: string | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbSearchCacheMaxAggregateOutputType = {
    query: string | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbSearchCacheCountAggregateOutputType = {
    query: number
    data: number
    createdAt: number
    _all: number
  }


  export type TmdbSearchCacheMinAggregateInputType = {
    query?: true
    data?: true
    createdAt?: true
  }

  export type TmdbSearchCacheMaxAggregateInputType = {
    query?: true
    data?: true
    createdAt?: true
  }

  export type TmdbSearchCacheCountAggregateInputType = {
    query?: true
    data?: true
    createdAt?: true
    _all?: true
  }

  export type TmdbSearchCacheAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbSearchCache to aggregate.
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSearchCaches to fetch.
     */
    orderBy?: TmdbSearchCacheOrderByWithRelationInput | TmdbSearchCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TmdbSearchCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSearchCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSearchCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TmdbSearchCaches
    **/
    _count?: true | TmdbSearchCacheCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TmdbSearchCacheMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TmdbSearchCacheMaxAggregateInputType
  }

  export type GetTmdbSearchCacheAggregateType<T extends TmdbSearchCacheAggregateArgs> = {
        [P in keyof T & keyof AggregateTmdbSearchCache]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTmdbSearchCache[P]>
      : GetScalarType<T[P], AggregateTmdbSearchCache[P]>
  }




  export type TmdbSearchCacheGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TmdbSearchCacheWhereInput
    orderBy?: TmdbSearchCacheOrderByWithAggregationInput | TmdbSearchCacheOrderByWithAggregationInput[]
    by: TmdbSearchCacheScalarFieldEnum[] | TmdbSearchCacheScalarFieldEnum
    having?: TmdbSearchCacheScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TmdbSearchCacheCountAggregateInputType | true
    _min?: TmdbSearchCacheMinAggregateInputType
    _max?: TmdbSearchCacheMaxAggregateInputType
  }

  export type TmdbSearchCacheGroupByOutputType = {
    query: string
    data: string
    createdAt: Date
    _count: TmdbSearchCacheCountAggregateOutputType | null
    _min: TmdbSearchCacheMinAggregateOutputType | null
    _max: TmdbSearchCacheMaxAggregateOutputType | null
  }

  type GetTmdbSearchCacheGroupByPayload<T extends TmdbSearchCacheGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TmdbSearchCacheGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TmdbSearchCacheGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TmdbSearchCacheGroupByOutputType[P]>
            : GetScalarType<T[P], TmdbSearchCacheGroupByOutputType[P]>
        }
      >
    >


  export type TmdbSearchCacheSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    query?: boolean
    data?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["tmdbSearchCache"]>

  export type TmdbSearchCacheSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    query?: boolean
    data?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["tmdbSearchCache"]>

  export type TmdbSearchCacheSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    query?: boolean
    data?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["tmdbSearchCache"]>

  export type TmdbSearchCacheSelectScalar = {
    query?: boolean
    data?: boolean
    createdAt?: boolean
  }

  export type TmdbSearchCacheOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"query" | "data" | "createdAt", ExtArgs["result"]["tmdbSearchCache"]>

  export type $TmdbSearchCachePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TmdbSearchCache"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      query: string
      data: string
      createdAt: Date
    }, ExtArgs["result"]["tmdbSearchCache"]>
    composites: {}
  }

  type TmdbSearchCacheGetPayload<S extends boolean | null | undefined | TmdbSearchCacheDefaultArgs> = $Result.GetResult<Prisma.$TmdbSearchCachePayload, S>

  type TmdbSearchCacheCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TmdbSearchCacheFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TmdbSearchCacheCountAggregateInputType | true
    }

  export interface TmdbSearchCacheDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TmdbSearchCache'], meta: { name: 'TmdbSearchCache' } }
    /**
     * Find zero or one TmdbSearchCache that matches the filter.
     * @param {TmdbSearchCacheFindUniqueArgs} args - Arguments to find a TmdbSearchCache
     * @example
     * // Get one TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TmdbSearchCacheFindUniqueArgs>(args: SelectSubset<T, TmdbSearchCacheFindUniqueArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TmdbSearchCache that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TmdbSearchCacheFindUniqueOrThrowArgs} args - Arguments to find a TmdbSearchCache
     * @example
     * // Get one TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TmdbSearchCacheFindUniqueOrThrowArgs>(args: SelectSubset<T, TmdbSearchCacheFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbSearchCache that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheFindFirstArgs} args - Arguments to find a TmdbSearchCache
     * @example
     * // Get one TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TmdbSearchCacheFindFirstArgs>(args?: SelectSubset<T, TmdbSearchCacheFindFirstArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbSearchCache that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheFindFirstOrThrowArgs} args - Arguments to find a TmdbSearchCache
     * @example
     * // Get one TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TmdbSearchCacheFindFirstOrThrowArgs>(args?: SelectSubset<T, TmdbSearchCacheFindFirstOrThrowArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TmdbSearchCaches that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TmdbSearchCaches
     * const tmdbSearchCaches = await prisma.tmdbSearchCache.findMany()
     * 
     * // Get first 10 TmdbSearchCaches
     * const tmdbSearchCaches = await prisma.tmdbSearchCache.findMany({ take: 10 })
     * 
     * // Only select the `query`
     * const tmdbSearchCacheWithQueryOnly = await prisma.tmdbSearchCache.findMany({ select: { query: true } })
     * 
     */
    findMany<T extends TmdbSearchCacheFindManyArgs>(args?: SelectSubset<T, TmdbSearchCacheFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TmdbSearchCache.
     * @param {TmdbSearchCacheCreateArgs} args - Arguments to create a TmdbSearchCache.
     * @example
     * // Create one TmdbSearchCache
     * const TmdbSearchCache = await prisma.tmdbSearchCache.create({
     *   data: {
     *     // ... data to create a TmdbSearchCache
     *   }
     * })
     * 
     */
    create<T extends TmdbSearchCacheCreateArgs>(args: SelectSubset<T, TmdbSearchCacheCreateArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TmdbSearchCaches.
     * @param {TmdbSearchCacheCreateManyArgs} args - Arguments to create many TmdbSearchCaches.
     * @example
     * // Create many TmdbSearchCaches
     * const tmdbSearchCache = await prisma.tmdbSearchCache.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TmdbSearchCacheCreateManyArgs>(args?: SelectSubset<T, TmdbSearchCacheCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TmdbSearchCaches and returns the data saved in the database.
     * @param {TmdbSearchCacheCreateManyAndReturnArgs} args - Arguments to create many TmdbSearchCaches.
     * @example
     * // Create many TmdbSearchCaches
     * const tmdbSearchCache = await prisma.tmdbSearchCache.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TmdbSearchCaches and only return the `query`
     * const tmdbSearchCacheWithQueryOnly = await prisma.tmdbSearchCache.createManyAndReturn({
     *   select: { query: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TmdbSearchCacheCreateManyAndReturnArgs>(args?: SelectSubset<T, TmdbSearchCacheCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TmdbSearchCache.
     * @param {TmdbSearchCacheDeleteArgs} args - Arguments to delete one TmdbSearchCache.
     * @example
     * // Delete one TmdbSearchCache
     * const TmdbSearchCache = await prisma.tmdbSearchCache.delete({
     *   where: {
     *     // ... filter to delete one TmdbSearchCache
     *   }
     * })
     * 
     */
    delete<T extends TmdbSearchCacheDeleteArgs>(args: SelectSubset<T, TmdbSearchCacheDeleteArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TmdbSearchCache.
     * @param {TmdbSearchCacheUpdateArgs} args - Arguments to update one TmdbSearchCache.
     * @example
     * // Update one TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TmdbSearchCacheUpdateArgs>(args: SelectSubset<T, TmdbSearchCacheUpdateArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TmdbSearchCaches.
     * @param {TmdbSearchCacheDeleteManyArgs} args - Arguments to filter TmdbSearchCaches to delete.
     * @example
     * // Delete a few TmdbSearchCaches
     * const { count } = await prisma.tmdbSearchCache.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TmdbSearchCacheDeleteManyArgs>(args?: SelectSubset<T, TmdbSearchCacheDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbSearchCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TmdbSearchCaches
     * const tmdbSearchCache = await prisma.tmdbSearchCache.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TmdbSearchCacheUpdateManyArgs>(args: SelectSubset<T, TmdbSearchCacheUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbSearchCaches and returns the data updated in the database.
     * @param {TmdbSearchCacheUpdateManyAndReturnArgs} args - Arguments to update many TmdbSearchCaches.
     * @example
     * // Update many TmdbSearchCaches
     * const tmdbSearchCache = await prisma.tmdbSearchCache.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TmdbSearchCaches and only return the `query`
     * const tmdbSearchCacheWithQueryOnly = await prisma.tmdbSearchCache.updateManyAndReturn({
     *   select: { query: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TmdbSearchCacheUpdateManyAndReturnArgs>(args: SelectSubset<T, TmdbSearchCacheUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TmdbSearchCache.
     * @param {TmdbSearchCacheUpsertArgs} args - Arguments to update or create a TmdbSearchCache.
     * @example
     * // Update or create a TmdbSearchCache
     * const tmdbSearchCache = await prisma.tmdbSearchCache.upsert({
     *   create: {
     *     // ... data to create a TmdbSearchCache
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TmdbSearchCache we want to update
     *   }
     * })
     */
    upsert<T extends TmdbSearchCacheUpsertArgs>(args: SelectSubset<T, TmdbSearchCacheUpsertArgs<ExtArgs>>): Prisma__TmdbSearchCacheClient<$Result.GetResult<Prisma.$TmdbSearchCachePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TmdbSearchCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheCountArgs} args - Arguments to filter TmdbSearchCaches to count.
     * @example
     * // Count the number of TmdbSearchCaches
     * const count = await prisma.tmdbSearchCache.count({
     *   where: {
     *     // ... the filter for the TmdbSearchCaches we want to count
     *   }
     * })
    **/
    count<T extends TmdbSearchCacheCountArgs>(
      args?: Subset<T, TmdbSearchCacheCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TmdbSearchCacheCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TmdbSearchCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TmdbSearchCacheAggregateArgs>(args: Subset<T, TmdbSearchCacheAggregateArgs>): Prisma.PrismaPromise<GetTmdbSearchCacheAggregateType<T>>

    /**
     * Group by TmdbSearchCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSearchCacheGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TmdbSearchCacheGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TmdbSearchCacheGroupByArgs['orderBy'] }
        : { orderBy?: TmdbSearchCacheGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TmdbSearchCacheGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTmdbSearchCacheGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TmdbSearchCache model
   */
  readonly fields: TmdbSearchCacheFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TmdbSearchCache.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TmdbSearchCacheClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TmdbSearchCache model
   */
  interface TmdbSearchCacheFieldRefs {
    readonly query: FieldRef<"TmdbSearchCache", 'String'>
    readonly data: FieldRef<"TmdbSearchCache", 'String'>
    readonly createdAt: FieldRef<"TmdbSearchCache", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TmdbSearchCache findUnique
   */
  export type TmdbSearchCacheFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter, which TmdbSearchCache to fetch.
     */
    where: TmdbSearchCacheWhereUniqueInput
  }

  /**
   * TmdbSearchCache findUniqueOrThrow
   */
  export type TmdbSearchCacheFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter, which TmdbSearchCache to fetch.
     */
    where: TmdbSearchCacheWhereUniqueInput
  }

  /**
   * TmdbSearchCache findFirst
   */
  export type TmdbSearchCacheFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter, which TmdbSearchCache to fetch.
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSearchCaches to fetch.
     */
    orderBy?: TmdbSearchCacheOrderByWithRelationInput | TmdbSearchCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbSearchCaches.
     */
    cursor?: TmdbSearchCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSearchCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSearchCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbSearchCaches.
     */
    distinct?: TmdbSearchCacheScalarFieldEnum | TmdbSearchCacheScalarFieldEnum[]
  }

  /**
   * TmdbSearchCache findFirstOrThrow
   */
  export type TmdbSearchCacheFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter, which TmdbSearchCache to fetch.
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSearchCaches to fetch.
     */
    orderBy?: TmdbSearchCacheOrderByWithRelationInput | TmdbSearchCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbSearchCaches.
     */
    cursor?: TmdbSearchCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSearchCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSearchCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbSearchCaches.
     */
    distinct?: TmdbSearchCacheScalarFieldEnum | TmdbSearchCacheScalarFieldEnum[]
  }

  /**
   * TmdbSearchCache findMany
   */
  export type TmdbSearchCacheFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter, which TmdbSearchCaches to fetch.
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSearchCaches to fetch.
     */
    orderBy?: TmdbSearchCacheOrderByWithRelationInput | TmdbSearchCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TmdbSearchCaches.
     */
    cursor?: TmdbSearchCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSearchCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSearchCaches.
     */
    skip?: number
    distinct?: TmdbSearchCacheScalarFieldEnum | TmdbSearchCacheScalarFieldEnum[]
  }

  /**
   * TmdbSearchCache create
   */
  export type TmdbSearchCacheCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * The data needed to create a TmdbSearchCache.
     */
    data: XOR<TmdbSearchCacheCreateInput, TmdbSearchCacheUncheckedCreateInput>
  }

  /**
   * TmdbSearchCache createMany
   */
  export type TmdbSearchCacheCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TmdbSearchCaches.
     */
    data: TmdbSearchCacheCreateManyInput | TmdbSearchCacheCreateManyInput[]
  }

  /**
   * TmdbSearchCache createManyAndReturn
   */
  export type TmdbSearchCacheCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * The data used to create many TmdbSearchCaches.
     */
    data: TmdbSearchCacheCreateManyInput | TmdbSearchCacheCreateManyInput[]
  }

  /**
   * TmdbSearchCache update
   */
  export type TmdbSearchCacheUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * The data needed to update a TmdbSearchCache.
     */
    data: XOR<TmdbSearchCacheUpdateInput, TmdbSearchCacheUncheckedUpdateInput>
    /**
     * Choose, which TmdbSearchCache to update.
     */
    where: TmdbSearchCacheWhereUniqueInput
  }

  /**
   * TmdbSearchCache updateMany
   */
  export type TmdbSearchCacheUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TmdbSearchCaches.
     */
    data: XOR<TmdbSearchCacheUpdateManyMutationInput, TmdbSearchCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbSearchCaches to update
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * Limit how many TmdbSearchCaches to update.
     */
    limit?: number
  }

  /**
   * TmdbSearchCache updateManyAndReturn
   */
  export type TmdbSearchCacheUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * The data used to update TmdbSearchCaches.
     */
    data: XOR<TmdbSearchCacheUpdateManyMutationInput, TmdbSearchCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbSearchCaches to update
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * Limit how many TmdbSearchCaches to update.
     */
    limit?: number
  }

  /**
   * TmdbSearchCache upsert
   */
  export type TmdbSearchCacheUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * The filter to search for the TmdbSearchCache to update in case it exists.
     */
    where: TmdbSearchCacheWhereUniqueInput
    /**
     * In case the TmdbSearchCache found by the `where` argument doesn't exist, create a new TmdbSearchCache with this data.
     */
    create: XOR<TmdbSearchCacheCreateInput, TmdbSearchCacheUncheckedCreateInput>
    /**
     * In case the TmdbSearchCache was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TmdbSearchCacheUpdateInput, TmdbSearchCacheUncheckedUpdateInput>
  }

  /**
   * TmdbSearchCache delete
   */
  export type TmdbSearchCacheDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
    /**
     * Filter which TmdbSearchCache to delete.
     */
    where: TmdbSearchCacheWhereUniqueInput
  }

  /**
   * TmdbSearchCache deleteMany
   */
  export type TmdbSearchCacheDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbSearchCaches to delete
     */
    where?: TmdbSearchCacheWhereInput
    /**
     * Limit how many TmdbSearchCaches to delete.
     */
    limit?: number
  }

  /**
   * TmdbSearchCache without action
   */
  export type TmdbSearchCacheDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSearchCache
     */
    select?: TmdbSearchCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSearchCache
     */
    omit?: TmdbSearchCacheOmit<ExtArgs> | null
  }


  /**
   * Model TmdbMediaCache
   */

  export type AggregateTmdbMediaCache = {
    _count: TmdbMediaCacheCountAggregateOutputType | null
    _avg: TmdbMediaCacheAvgAggregateOutputType | null
    _sum: TmdbMediaCacheSumAggregateOutputType | null
    _min: TmdbMediaCacheMinAggregateOutputType | null
    _max: TmdbMediaCacheMaxAggregateOutputType | null
  }

  export type TmdbMediaCacheAvgAggregateOutputType = {
    tmdbId: number | null
  }

  export type TmdbMediaCacheSumAggregateOutputType = {
    tmdbId: number | null
  }

  export type TmdbMediaCacheMinAggregateOutputType = {
    tmdbId: number | null
    mediaType: $Enums.TmdbMediaType | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbMediaCacheMaxAggregateOutputType = {
    tmdbId: number | null
    mediaType: $Enums.TmdbMediaType | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbMediaCacheCountAggregateOutputType = {
    tmdbId: number
    mediaType: number
    data: number
    createdAt: number
    _all: number
  }


  export type TmdbMediaCacheAvgAggregateInputType = {
    tmdbId?: true
  }

  export type TmdbMediaCacheSumAggregateInputType = {
    tmdbId?: true
  }

  export type TmdbMediaCacheMinAggregateInputType = {
    tmdbId?: true
    mediaType?: true
    data?: true
    createdAt?: true
  }

  export type TmdbMediaCacheMaxAggregateInputType = {
    tmdbId?: true
    mediaType?: true
    data?: true
    createdAt?: true
  }

  export type TmdbMediaCacheCountAggregateInputType = {
    tmdbId?: true
    mediaType?: true
    data?: true
    createdAt?: true
    _all?: true
  }

  export type TmdbMediaCacheAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbMediaCache to aggregate.
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbMediaCaches to fetch.
     */
    orderBy?: TmdbMediaCacheOrderByWithRelationInput | TmdbMediaCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TmdbMediaCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbMediaCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbMediaCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TmdbMediaCaches
    **/
    _count?: true | TmdbMediaCacheCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: TmdbMediaCacheAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: TmdbMediaCacheSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TmdbMediaCacheMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TmdbMediaCacheMaxAggregateInputType
  }

  export type GetTmdbMediaCacheAggregateType<T extends TmdbMediaCacheAggregateArgs> = {
        [P in keyof T & keyof AggregateTmdbMediaCache]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTmdbMediaCache[P]>
      : GetScalarType<T[P], AggregateTmdbMediaCache[P]>
  }




  export type TmdbMediaCacheGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TmdbMediaCacheWhereInput
    orderBy?: TmdbMediaCacheOrderByWithAggregationInput | TmdbMediaCacheOrderByWithAggregationInput[]
    by: TmdbMediaCacheScalarFieldEnum[] | TmdbMediaCacheScalarFieldEnum
    having?: TmdbMediaCacheScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TmdbMediaCacheCountAggregateInputType | true
    _avg?: TmdbMediaCacheAvgAggregateInputType
    _sum?: TmdbMediaCacheSumAggregateInputType
    _min?: TmdbMediaCacheMinAggregateInputType
    _max?: TmdbMediaCacheMaxAggregateInputType
  }

  export type TmdbMediaCacheGroupByOutputType = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt: Date
    _count: TmdbMediaCacheCountAggregateOutputType | null
    _avg: TmdbMediaCacheAvgAggregateOutputType | null
    _sum: TmdbMediaCacheSumAggregateOutputType | null
    _min: TmdbMediaCacheMinAggregateOutputType | null
    _max: TmdbMediaCacheMaxAggregateOutputType | null
  }

  type GetTmdbMediaCacheGroupByPayload<T extends TmdbMediaCacheGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TmdbMediaCacheGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TmdbMediaCacheGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TmdbMediaCacheGroupByOutputType[P]>
            : GetScalarType<T[P], TmdbMediaCacheGroupByOutputType[P]>
        }
      >
    >


  export type TmdbMediaCacheSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tmdbId?: boolean
    mediaType?: boolean
    data?: boolean
    createdAt?: boolean
    seasons?: boolean | TmdbMediaCache$seasonsArgs<ExtArgs>
    _count?: boolean | TmdbMediaCacheCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["tmdbMediaCache"]>

  export type TmdbMediaCacheSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tmdbId?: boolean
    mediaType?: boolean
    data?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["tmdbMediaCache"]>

  export type TmdbMediaCacheSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tmdbId?: boolean
    mediaType?: boolean
    data?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["tmdbMediaCache"]>

  export type TmdbMediaCacheSelectScalar = {
    tmdbId?: boolean
    mediaType?: boolean
    data?: boolean
    createdAt?: boolean
  }

  export type TmdbMediaCacheOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"tmdbId" | "mediaType" | "data" | "createdAt", ExtArgs["result"]["tmdbMediaCache"]>
  export type TmdbMediaCacheInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    seasons?: boolean | TmdbMediaCache$seasonsArgs<ExtArgs>
    _count?: boolean | TmdbMediaCacheCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type TmdbMediaCacheIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type TmdbMediaCacheIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $TmdbMediaCachePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TmdbMediaCache"
    objects: {
      seasons: Prisma.$TmdbSeasonCachePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      tmdbId: number
      mediaType: $Enums.TmdbMediaType
      data: string
      createdAt: Date
    }, ExtArgs["result"]["tmdbMediaCache"]>
    composites: {}
  }

  type TmdbMediaCacheGetPayload<S extends boolean | null | undefined | TmdbMediaCacheDefaultArgs> = $Result.GetResult<Prisma.$TmdbMediaCachePayload, S>

  type TmdbMediaCacheCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TmdbMediaCacheFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TmdbMediaCacheCountAggregateInputType | true
    }

  export interface TmdbMediaCacheDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TmdbMediaCache'], meta: { name: 'TmdbMediaCache' } }
    /**
     * Find zero or one TmdbMediaCache that matches the filter.
     * @param {TmdbMediaCacheFindUniqueArgs} args - Arguments to find a TmdbMediaCache
     * @example
     * // Get one TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TmdbMediaCacheFindUniqueArgs>(args: SelectSubset<T, TmdbMediaCacheFindUniqueArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TmdbMediaCache that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TmdbMediaCacheFindUniqueOrThrowArgs} args - Arguments to find a TmdbMediaCache
     * @example
     * // Get one TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TmdbMediaCacheFindUniqueOrThrowArgs>(args: SelectSubset<T, TmdbMediaCacheFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbMediaCache that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheFindFirstArgs} args - Arguments to find a TmdbMediaCache
     * @example
     * // Get one TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TmdbMediaCacheFindFirstArgs>(args?: SelectSubset<T, TmdbMediaCacheFindFirstArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbMediaCache that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheFindFirstOrThrowArgs} args - Arguments to find a TmdbMediaCache
     * @example
     * // Get one TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TmdbMediaCacheFindFirstOrThrowArgs>(args?: SelectSubset<T, TmdbMediaCacheFindFirstOrThrowArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TmdbMediaCaches that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TmdbMediaCaches
     * const tmdbMediaCaches = await prisma.tmdbMediaCache.findMany()
     * 
     * // Get first 10 TmdbMediaCaches
     * const tmdbMediaCaches = await prisma.tmdbMediaCache.findMany({ take: 10 })
     * 
     * // Only select the `tmdbId`
     * const tmdbMediaCacheWithTmdbIdOnly = await prisma.tmdbMediaCache.findMany({ select: { tmdbId: true } })
     * 
     */
    findMany<T extends TmdbMediaCacheFindManyArgs>(args?: SelectSubset<T, TmdbMediaCacheFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TmdbMediaCache.
     * @param {TmdbMediaCacheCreateArgs} args - Arguments to create a TmdbMediaCache.
     * @example
     * // Create one TmdbMediaCache
     * const TmdbMediaCache = await prisma.tmdbMediaCache.create({
     *   data: {
     *     // ... data to create a TmdbMediaCache
     *   }
     * })
     * 
     */
    create<T extends TmdbMediaCacheCreateArgs>(args: SelectSubset<T, TmdbMediaCacheCreateArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TmdbMediaCaches.
     * @param {TmdbMediaCacheCreateManyArgs} args - Arguments to create many TmdbMediaCaches.
     * @example
     * // Create many TmdbMediaCaches
     * const tmdbMediaCache = await prisma.tmdbMediaCache.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TmdbMediaCacheCreateManyArgs>(args?: SelectSubset<T, TmdbMediaCacheCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TmdbMediaCaches and returns the data saved in the database.
     * @param {TmdbMediaCacheCreateManyAndReturnArgs} args - Arguments to create many TmdbMediaCaches.
     * @example
     * // Create many TmdbMediaCaches
     * const tmdbMediaCache = await prisma.tmdbMediaCache.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TmdbMediaCaches and only return the `tmdbId`
     * const tmdbMediaCacheWithTmdbIdOnly = await prisma.tmdbMediaCache.createManyAndReturn({
     *   select: { tmdbId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TmdbMediaCacheCreateManyAndReturnArgs>(args?: SelectSubset<T, TmdbMediaCacheCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TmdbMediaCache.
     * @param {TmdbMediaCacheDeleteArgs} args - Arguments to delete one TmdbMediaCache.
     * @example
     * // Delete one TmdbMediaCache
     * const TmdbMediaCache = await prisma.tmdbMediaCache.delete({
     *   where: {
     *     // ... filter to delete one TmdbMediaCache
     *   }
     * })
     * 
     */
    delete<T extends TmdbMediaCacheDeleteArgs>(args: SelectSubset<T, TmdbMediaCacheDeleteArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TmdbMediaCache.
     * @param {TmdbMediaCacheUpdateArgs} args - Arguments to update one TmdbMediaCache.
     * @example
     * // Update one TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TmdbMediaCacheUpdateArgs>(args: SelectSubset<T, TmdbMediaCacheUpdateArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TmdbMediaCaches.
     * @param {TmdbMediaCacheDeleteManyArgs} args - Arguments to filter TmdbMediaCaches to delete.
     * @example
     * // Delete a few TmdbMediaCaches
     * const { count } = await prisma.tmdbMediaCache.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TmdbMediaCacheDeleteManyArgs>(args?: SelectSubset<T, TmdbMediaCacheDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbMediaCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TmdbMediaCaches
     * const tmdbMediaCache = await prisma.tmdbMediaCache.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TmdbMediaCacheUpdateManyArgs>(args: SelectSubset<T, TmdbMediaCacheUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbMediaCaches and returns the data updated in the database.
     * @param {TmdbMediaCacheUpdateManyAndReturnArgs} args - Arguments to update many TmdbMediaCaches.
     * @example
     * // Update many TmdbMediaCaches
     * const tmdbMediaCache = await prisma.tmdbMediaCache.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TmdbMediaCaches and only return the `tmdbId`
     * const tmdbMediaCacheWithTmdbIdOnly = await prisma.tmdbMediaCache.updateManyAndReturn({
     *   select: { tmdbId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TmdbMediaCacheUpdateManyAndReturnArgs>(args: SelectSubset<T, TmdbMediaCacheUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TmdbMediaCache.
     * @param {TmdbMediaCacheUpsertArgs} args - Arguments to update or create a TmdbMediaCache.
     * @example
     * // Update or create a TmdbMediaCache
     * const tmdbMediaCache = await prisma.tmdbMediaCache.upsert({
     *   create: {
     *     // ... data to create a TmdbMediaCache
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TmdbMediaCache we want to update
     *   }
     * })
     */
    upsert<T extends TmdbMediaCacheUpsertArgs>(args: SelectSubset<T, TmdbMediaCacheUpsertArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TmdbMediaCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheCountArgs} args - Arguments to filter TmdbMediaCaches to count.
     * @example
     * // Count the number of TmdbMediaCaches
     * const count = await prisma.tmdbMediaCache.count({
     *   where: {
     *     // ... the filter for the TmdbMediaCaches we want to count
     *   }
     * })
    **/
    count<T extends TmdbMediaCacheCountArgs>(
      args?: Subset<T, TmdbMediaCacheCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TmdbMediaCacheCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TmdbMediaCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TmdbMediaCacheAggregateArgs>(args: Subset<T, TmdbMediaCacheAggregateArgs>): Prisma.PrismaPromise<GetTmdbMediaCacheAggregateType<T>>

    /**
     * Group by TmdbMediaCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbMediaCacheGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TmdbMediaCacheGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TmdbMediaCacheGroupByArgs['orderBy'] }
        : { orderBy?: TmdbMediaCacheGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TmdbMediaCacheGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTmdbMediaCacheGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TmdbMediaCache model
   */
  readonly fields: TmdbMediaCacheFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TmdbMediaCache.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TmdbMediaCacheClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    seasons<T extends TmdbMediaCache$seasonsArgs<ExtArgs> = {}>(args?: Subset<T, TmdbMediaCache$seasonsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TmdbMediaCache model
   */
  interface TmdbMediaCacheFieldRefs {
    readonly tmdbId: FieldRef<"TmdbMediaCache", 'Int'>
    readonly mediaType: FieldRef<"TmdbMediaCache", 'TmdbMediaType'>
    readonly data: FieldRef<"TmdbMediaCache", 'String'>
    readonly createdAt: FieldRef<"TmdbMediaCache", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TmdbMediaCache findUnique
   */
  export type TmdbMediaCacheFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbMediaCache to fetch.
     */
    where: TmdbMediaCacheWhereUniqueInput
  }

  /**
   * TmdbMediaCache findUniqueOrThrow
   */
  export type TmdbMediaCacheFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbMediaCache to fetch.
     */
    where: TmdbMediaCacheWhereUniqueInput
  }

  /**
   * TmdbMediaCache findFirst
   */
  export type TmdbMediaCacheFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbMediaCache to fetch.
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbMediaCaches to fetch.
     */
    orderBy?: TmdbMediaCacheOrderByWithRelationInput | TmdbMediaCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbMediaCaches.
     */
    cursor?: TmdbMediaCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbMediaCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbMediaCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbMediaCaches.
     */
    distinct?: TmdbMediaCacheScalarFieldEnum | TmdbMediaCacheScalarFieldEnum[]
  }

  /**
   * TmdbMediaCache findFirstOrThrow
   */
  export type TmdbMediaCacheFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbMediaCache to fetch.
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbMediaCaches to fetch.
     */
    orderBy?: TmdbMediaCacheOrderByWithRelationInput | TmdbMediaCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbMediaCaches.
     */
    cursor?: TmdbMediaCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbMediaCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbMediaCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbMediaCaches.
     */
    distinct?: TmdbMediaCacheScalarFieldEnum | TmdbMediaCacheScalarFieldEnum[]
  }

  /**
   * TmdbMediaCache findMany
   */
  export type TmdbMediaCacheFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbMediaCaches to fetch.
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbMediaCaches to fetch.
     */
    orderBy?: TmdbMediaCacheOrderByWithRelationInput | TmdbMediaCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TmdbMediaCaches.
     */
    cursor?: TmdbMediaCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbMediaCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbMediaCaches.
     */
    skip?: number
    distinct?: TmdbMediaCacheScalarFieldEnum | TmdbMediaCacheScalarFieldEnum[]
  }

  /**
   * TmdbMediaCache create
   */
  export type TmdbMediaCacheCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * The data needed to create a TmdbMediaCache.
     */
    data: XOR<TmdbMediaCacheCreateInput, TmdbMediaCacheUncheckedCreateInput>
  }

  /**
   * TmdbMediaCache createMany
   */
  export type TmdbMediaCacheCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TmdbMediaCaches.
     */
    data: TmdbMediaCacheCreateManyInput | TmdbMediaCacheCreateManyInput[]
  }

  /**
   * TmdbMediaCache createManyAndReturn
   */
  export type TmdbMediaCacheCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * The data used to create many TmdbMediaCaches.
     */
    data: TmdbMediaCacheCreateManyInput | TmdbMediaCacheCreateManyInput[]
  }

  /**
   * TmdbMediaCache update
   */
  export type TmdbMediaCacheUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * The data needed to update a TmdbMediaCache.
     */
    data: XOR<TmdbMediaCacheUpdateInput, TmdbMediaCacheUncheckedUpdateInput>
    /**
     * Choose, which TmdbMediaCache to update.
     */
    where: TmdbMediaCacheWhereUniqueInput
  }

  /**
   * TmdbMediaCache updateMany
   */
  export type TmdbMediaCacheUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TmdbMediaCaches.
     */
    data: XOR<TmdbMediaCacheUpdateManyMutationInput, TmdbMediaCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbMediaCaches to update
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * Limit how many TmdbMediaCaches to update.
     */
    limit?: number
  }

  /**
   * TmdbMediaCache updateManyAndReturn
   */
  export type TmdbMediaCacheUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * The data used to update TmdbMediaCaches.
     */
    data: XOR<TmdbMediaCacheUpdateManyMutationInput, TmdbMediaCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbMediaCaches to update
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * Limit how many TmdbMediaCaches to update.
     */
    limit?: number
  }

  /**
   * TmdbMediaCache upsert
   */
  export type TmdbMediaCacheUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * The filter to search for the TmdbMediaCache to update in case it exists.
     */
    where: TmdbMediaCacheWhereUniqueInput
    /**
     * In case the TmdbMediaCache found by the `where` argument doesn't exist, create a new TmdbMediaCache with this data.
     */
    create: XOR<TmdbMediaCacheCreateInput, TmdbMediaCacheUncheckedCreateInput>
    /**
     * In case the TmdbMediaCache was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TmdbMediaCacheUpdateInput, TmdbMediaCacheUncheckedUpdateInput>
  }

  /**
   * TmdbMediaCache delete
   */
  export type TmdbMediaCacheDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
    /**
     * Filter which TmdbMediaCache to delete.
     */
    where: TmdbMediaCacheWhereUniqueInput
  }

  /**
   * TmdbMediaCache deleteMany
   */
  export type TmdbMediaCacheDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbMediaCaches to delete
     */
    where?: TmdbMediaCacheWhereInput
    /**
     * Limit how many TmdbMediaCaches to delete.
     */
    limit?: number
  }

  /**
   * TmdbMediaCache.seasons
   */
  export type TmdbMediaCache$seasonsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    where?: TmdbSeasonCacheWhereInput
    orderBy?: TmdbSeasonCacheOrderByWithRelationInput | TmdbSeasonCacheOrderByWithRelationInput[]
    cursor?: TmdbSeasonCacheWhereUniqueInput
    take?: number
    skip?: number
    distinct?: TmdbSeasonCacheScalarFieldEnum | TmdbSeasonCacheScalarFieldEnum[]
  }

  /**
   * TmdbMediaCache without action
   */
  export type TmdbMediaCacheDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbMediaCache
     */
    select?: TmdbMediaCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbMediaCache
     */
    omit?: TmdbMediaCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbMediaCacheInclude<ExtArgs> | null
  }


  /**
   * Model TmdbSeasonCache
   */

  export type AggregateTmdbSeasonCache = {
    _count: TmdbSeasonCacheCountAggregateOutputType | null
    _avg: TmdbSeasonCacheAvgAggregateOutputType | null
    _sum: TmdbSeasonCacheSumAggregateOutputType | null
    _min: TmdbSeasonCacheMinAggregateOutputType | null
    _max: TmdbSeasonCacheMaxAggregateOutputType | null
  }

  export type TmdbSeasonCacheAvgAggregateOutputType = {
    tvId: number | null
    seasonNumber: number | null
  }

  export type TmdbSeasonCacheSumAggregateOutputType = {
    tvId: number | null
    seasonNumber: number | null
  }

  export type TmdbSeasonCacheMinAggregateOutputType = {
    tvId: number | null
    seasonNumber: number | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbSeasonCacheMaxAggregateOutputType = {
    tvId: number | null
    seasonNumber: number | null
    data: string | null
    createdAt: Date | null
  }

  export type TmdbSeasonCacheCountAggregateOutputType = {
    tvId: number
    seasonNumber: number
    data: number
    createdAt: number
    _all: number
  }


  export type TmdbSeasonCacheAvgAggregateInputType = {
    tvId?: true
    seasonNumber?: true
  }

  export type TmdbSeasonCacheSumAggregateInputType = {
    tvId?: true
    seasonNumber?: true
  }

  export type TmdbSeasonCacheMinAggregateInputType = {
    tvId?: true
    seasonNumber?: true
    data?: true
    createdAt?: true
  }

  export type TmdbSeasonCacheMaxAggregateInputType = {
    tvId?: true
    seasonNumber?: true
    data?: true
    createdAt?: true
  }

  export type TmdbSeasonCacheCountAggregateInputType = {
    tvId?: true
    seasonNumber?: true
    data?: true
    createdAt?: true
    _all?: true
  }

  export type TmdbSeasonCacheAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbSeasonCache to aggregate.
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSeasonCaches to fetch.
     */
    orderBy?: TmdbSeasonCacheOrderByWithRelationInput | TmdbSeasonCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TmdbSeasonCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSeasonCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSeasonCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned TmdbSeasonCaches
    **/
    _count?: true | TmdbSeasonCacheCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: TmdbSeasonCacheAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: TmdbSeasonCacheSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TmdbSeasonCacheMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TmdbSeasonCacheMaxAggregateInputType
  }

  export type GetTmdbSeasonCacheAggregateType<T extends TmdbSeasonCacheAggregateArgs> = {
        [P in keyof T & keyof AggregateTmdbSeasonCache]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTmdbSeasonCache[P]>
      : GetScalarType<T[P], AggregateTmdbSeasonCache[P]>
  }




  export type TmdbSeasonCacheGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TmdbSeasonCacheWhereInput
    orderBy?: TmdbSeasonCacheOrderByWithAggregationInput | TmdbSeasonCacheOrderByWithAggregationInput[]
    by: TmdbSeasonCacheScalarFieldEnum[] | TmdbSeasonCacheScalarFieldEnum
    having?: TmdbSeasonCacheScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TmdbSeasonCacheCountAggregateInputType | true
    _avg?: TmdbSeasonCacheAvgAggregateInputType
    _sum?: TmdbSeasonCacheSumAggregateInputType
    _min?: TmdbSeasonCacheMinAggregateInputType
    _max?: TmdbSeasonCacheMaxAggregateInputType
  }

  export type TmdbSeasonCacheGroupByOutputType = {
    tvId: number
    seasonNumber: number
    data: string
    createdAt: Date
    _count: TmdbSeasonCacheCountAggregateOutputType | null
    _avg: TmdbSeasonCacheAvgAggregateOutputType | null
    _sum: TmdbSeasonCacheSumAggregateOutputType | null
    _min: TmdbSeasonCacheMinAggregateOutputType | null
    _max: TmdbSeasonCacheMaxAggregateOutputType | null
  }

  type GetTmdbSeasonCacheGroupByPayload<T extends TmdbSeasonCacheGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TmdbSeasonCacheGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TmdbSeasonCacheGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TmdbSeasonCacheGroupByOutputType[P]>
            : GetScalarType<T[P], TmdbSeasonCacheGroupByOutputType[P]>
        }
      >
    >


  export type TmdbSeasonCacheSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tvId?: boolean
    seasonNumber?: boolean
    data?: boolean
    createdAt?: boolean
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["tmdbSeasonCache"]>

  export type TmdbSeasonCacheSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tvId?: boolean
    seasonNumber?: boolean
    data?: boolean
    createdAt?: boolean
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["tmdbSeasonCache"]>

  export type TmdbSeasonCacheSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    tvId?: boolean
    seasonNumber?: boolean
    data?: boolean
    createdAt?: boolean
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["tmdbSeasonCache"]>

  export type TmdbSeasonCacheSelectScalar = {
    tvId?: boolean
    seasonNumber?: boolean
    data?: boolean
    createdAt?: boolean
  }

  export type TmdbSeasonCacheOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"tvId" | "seasonNumber" | "data" | "createdAt", ExtArgs["result"]["tmdbSeasonCache"]>
  export type TmdbSeasonCacheInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }
  export type TmdbSeasonCacheIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }
  export type TmdbSeasonCacheIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    tvShow?: boolean | TmdbMediaCacheDefaultArgs<ExtArgs>
  }

  export type $TmdbSeasonCachePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "TmdbSeasonCache"
    objects: {
      tvShow: Prisma.$TmdbMediaCachePayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      tvId: number
      seasonNumber: number
      data: string
      createdAt: Date
    }, ExtArgs["result"]["tmdbSeasonCache"]>
    composites: {}
  }

  type TmdbSeasonCacheGetPayload<S extends boolean | null | undefined | TmdbSeasonCacheDefaultArgs> = $Result.GetResult<Prisma.$TmdbSeasonCachePayload, S>

  type TmdbSeasonCacheCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TmdbSeasonCacheFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TmdbSeasonCacheCountAggregateInputType | true
    }

  export interface TmdbSeasonCacheDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TmdbSeasonCache'], meta: { name: 'TmdbSeasonCache' } }
    /**
     * Find zero or one TmdbSeasonCache that matches the filter.
     * @param {TmdbSeasonCacheFindUniqueArgs} args - Arguments to find a TmdbSeasonCache
     * @example
     * // Get one TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TmdbSeasonCacheFindUniqueArgs>(args: SelectSubset<T, TmdbSeasonCacheFindUniqueArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one TmdbSeasonCache that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TmdbSeasonCacheFindUniqueOrThrowArgs} args - Arguments to find a TmdbSeasonCache
     * @example
     * // Get one TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TmdbSeasonCacheFindUniqueOrThrowArgs>(args: SelectSubset<T, TmdbSeasonCacheFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbSeasonCache that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheFindFirstArgs} args - Arguments to find a TmdbSeasonCache
     * @example
     * // Get one TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TmdbSeasonCacheFindFirstArgs>(args?: SelectSubset<T, TmdbSeasonCacheFindFirstArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first TmdbSeasonCache that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheFindFirstOrThrowArgs} args - Arguments to find a TmdbSeasonCache
     * @example
     * // Get one TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TmdbSeasonCacheFindFirstOrThrowArgs>(args?: SelectSubset<T, TmdbSeasonCacheFindFirstOrThrowArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more TmdbSeasonCaches that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all TmdbSeasonCaches
     * const tmdbSeasonCaches = await prisma.tmdbSeasonCache.findMany()
     * 
     * // Get first 10 TmdbSeasonCaches
     * const tmdbSeasonCaches = await prisma.tmdbSeasonCache.findMany({ take: 10 })
     * 
     * // Only select the `tvId`
     * const tmdbSeasonCacheWithTvIdOnly = await prisma.tmdbSeasonCache.findMany({ select: { tvId: true } })
     * 
     */
    findMany<T extends TmdbSeasonCacheFindManyArgs>(args?: SelectSubset<T, TmdbSeasonCacheFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a TmdbSeasonCache.
     * @param {TmdbSeasonCacheCreateArgs} args - Arguments to create a TmdbSeasonCache.
     * @example
     * // Create one TmdbSeasonCache
     * const TmdbSeasonCache = await prisma.tmdbSeasonCache.create({
     *   data: {
     *     // ... data to create a TmdbSeasonCache
     *   }
     * })
     * 
     */
    create<T extends TmdbSeasonCacheCreateArgs>(args: SelectSubset<T, TmdbSeasonCacheCreateArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many TmdbSeasonCaches.
     * @param {TmdbSeasonCacheCreateManyArgs} args - Arguments to create many TmdbSeasonCaches.
     * @example
     * // Create many TmdbSeasonCaches
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TmdbSeasonCacheCreateManyArgs>(args?: SelectSubset<T, TmdbSeasonCacheCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many TmdbSeasonCaches and returns the data saved in the database.
     * @param {TmdbSeasonCacheCreateManyAndReturnArgs} args - Arguments to create many TmdbSeasonCaches.
     * @example
     * // Create many TmdbSeasonCaches
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many TmdbSeasonCaches and only return the `tvId`
     * const tmdbSeasonCacheWithTvIdOnly = await prisma.tmdbSeasonCache.createManyAndReturn({
     *   select: { tvId: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TmdbSeasonCacheCreateManyAndReturnArgs>(args?: SelectSubset<T, TmdbSeasonCacheCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a TmdbSeasonCache.
     * @param {TmdbSeasonCacheDeleteArgs} args - Arguments to delete one TmdbSeasonCache.
     * @example
     * // Delete one TmdbSeasonCache
     * const TmdbSeasonCache = await prisma.tmdbSeasonCache.delete({
     *   where: {
     *     // ... filter to delete one TmdbSeasonCache
     *   }
     * })
     * 
     */
    delete<T extends TmdbSeasonCacheDeleteArgs>(args: SelectSubset<T, TmdbSeasonCacheDeleteArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one TmdbSeasonCache.
     * @param {TmdbSeasonCacheUpdateArgs} args - Arguments to update one TmdbSeasonCache.
     * @example
     * // Update one TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TmdbSeasonCacheUpdateArgs>(args: SelectSubset<T, TmdbSeasonCacheUpdateArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more TmdbSeasonCaches.
     * @param {TmdbSeasonCacheDeleteManyArgs} args - Arguments to filter TmdbSeasonCaches to delete.
     * @example
     * // Delete a few TmdbSeasonCaches
     * const { count } = await prisma.tmdbSeasonCache.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TmdbSeasonCacheDeleteManyArgs>(args?: SelectSubset<T, TmdbSeasonCacheDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbSeasonCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many TmdbSeasonCaches
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TmdbSeasonCacheUpdateManyArgs>(args: SelectSubset<T, TmdbSeasonCacheUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more TmdbSeasonCaches and returns the data updated in the database.
     * @param {TmdbSeasonCacheUpdateManyAndReturnArgs} args - Arguments to update many TmdbSeasonCaches.
     * @example
     * // Update many TmdbSeasonCaches
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more TmdbSeasonCaches and only return the `tvId`
     * const tmdbSeasonCacheWithTvIdOnly = await prisma.tmdbSeasonCache.updateManyAndReturn({
     *   select: { tvId: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TmdbSeasonCacheUpdateManyAndReturnArgs>(args: SelectSubset<T, TmdbSeasonCacheUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one TmdbSeasonCache.
     * @param {TmdbSeasonCacheUpsertArgs} args - Arguments to update or create a TmdbSeasonCache.
     * @example
     * // Update or create a TmdbSeasonCache
     * const tmdbSeasonCache = await prisma.tmdbSeasonCache.upsert({
     *   create: {
     *     // ... data to create a TmdbSeasonCache
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the TmdbSeasonCache we want to update
     *   }
     * })
     */
    upsert<T extends TmdbSeasonCacheUpsertArgs>(args: SelectSubset<T, TmdbSeasonCacheUpsertArgs<ExtArgs>>): Prisma__TmdbSeasonCacheClient<$Result.GetResult<Prisma.$TmdbSeasonCachePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of TmdbSeasonCaches.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheCountArgs} args - Arguments to filter TmdbSeasonCaches to count.
     * @example
     * // Count the number of TmdbSeasonCaches
     * const count = await prisma.tmdbSeasonCache.count({
     *   where: {
     *     // ... the filter for the TmdbSeasonCaches we want to count
     *   }
     * })
    **/
    count<T extends TmdbSeasonCacheCountArgs>(
      args?: Subset<T, TmdbSeasonCacheCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TmdbSeasonCacheCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a TmdbSeasonCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TmdbSeasonCacheAggregateArgs>(args: Subset<T, TmdbSeasonCacheAggregateArgs>): Prisma.PrismaPromise<GetTmdbSeasonCacheAggregateType<T>>

    /**
     * Group by TmdbSeasonCache.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TmdbSeasonCacheGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TmdbSeasonCacheGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TmdbSeasonCacheGroupByArgs['orderBy'] }
        : { orderBy?: TmdbSeasonCacheGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TmdbSeasonCacheGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTmdbSeasonCacheGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the TmdbSeasonCache model
   */
  readonly fields: TmdbSeasonCacheFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for TmdbSeasonCache.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TmdbSeasonCacheClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    tvShow<T extends TmdbMediaCacheDefaultArgs<ExtArgs> = {}>(args?: Subset<T, TmdbMediaCacheDefaultArgs<ExtArgs>>): Prisma__TmdbMediaCacheClient<$Result.GetResult<Prisma.$TmdbMediaCachePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the TmdbSeasonCache model
   */
  interface TmdbSeasonCacheFieldRefs {
    readonly tvId: FieldRef<"TmdbSeasonCache", 'Int'>
    readonly seasonNumber: FieldRef<"TmdbSeasonCache", 'Int'>
    readonly data: FieldRef<"TmdbSeasonCache", 'String'>
    readonly createdAt: FieldRef<"TmdbSeasonCache", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * TmdbSeasonCache findUnique
   */
  export type TmdbSeasonCacheFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbSeasonCache to fetch.
     */
    where: TmdbSeasonCacheWhereUniqueInput
  }

  /**
   * TmdbSeasonCache findUniqueOrThrow
   */
  export type TmdbSeasonCacheFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbSeasonCache to fetch.
     */
    where: TmdbSeasonCacheWhereUniqueInput
  }

  /**
   * TmdbSeasonCache findFirst
   */
  export type TmdbSeasonCacheFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbSeasonCache to fetch.
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSeasonCaches to fetch.
     */
    orderBy?: TmdbSeasonCacheOrderByWithRelationInput | TmdbSeasonCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbSeasonCaches.
     */
    cursor?: TmdbSeasonCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSeasonCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSeasonCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbSeasonCaches.
     */
    distinct?: TmdbSeasonCacheScalarFieldEnum | TmdbSeasonCacheScalarFieldEnum[]
  }

  /**
   * TmdbSeasonCache findFirstOrThrow
   */
  export type TmdbSeasonCacheFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbSeasonCache to fetch.
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSeasonCaches to fetch.
     */
    orderBy?: TmdbSeasonCacheOrderByWithRelationInput | TmdbSeasonCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for TmdbSeasonCaches.
     */
    cursor?: TmdbSeasonCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSeasonCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSeasonCaches.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of TmdbSeasonCaches.
     */
    distinct?: TmdbSeasonCacheScalarFieldEnum | TmdbSeasonCacheScalarFieldEnum[]
  }

  /**
   * TmdbSeasonCache findMany
   */
  export type TmdbSeasonCacheFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter, which TmdbSeasonCaches to fetch.
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of TmdbSeasonCaches to fetch.
     */
    orderBy?: TmdbSeasonCacheOrderByWithRelationInput | TmdbSeasonCacheOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing TmdbSeasonCaches.
     */
    cursor?: TmdbSeasonCacheWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` TmdbSeasonCaches from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` TmdbSeasonCaches.
     */
    skip?: number
    distinct?: TmdbSeasonCacheScalarFieldEnum | TmdbSeasonCacheScalarFieldEnum[]
  }

  /**
   * TmdbSeasonCache create
   */
  export type TmdbSeasonCacheCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * The data needed to create a TmdbSeasonCache.
     */
    data: XOR<TmdbSeasonCacheCreateInput, TmdbSeasonCacheUncheckedCreateInput>
  }

  /**
   * TmdbSeasonCache createMany
   */
  export type TmdbSeasonCacheCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many TmdbSeasonCaches.
     */
    data: TmdbSeasonCacheCreateManyInput | TmdbSeasonCacheCreateManyInput[]
  }

  /**
   * TmdbSeasonCache createManyAndReturn
   */
  export type TmdbSeasonCacheCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * The data used to create many TmdbSeasonCaches.
     */
    data: TmdbSeasonCacheCreateManyInput | TmdbSeasonCacheCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * TmdbSeasonCache update
   */
  export type TmdbSeasonCacheUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * The data needed to update a TmdbSeasonCache.
     */
    data: XOR<TmdbSeasonCacheUpdateInput, TmdbSeasonCacheUncheckedUpdateInput>
    /**
     * Choose, which TmdbSeasonCache to update.
     */
    where: TmdbSeasonCacheWhereUniqueInput
  }

  /**
   * TmdbSeasonCache updateMany
   */
  export type TmdbSeasonCacheUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update TmdbSeasonCaches.
     */
    data: XOR<TmdbSeasonCacheUpdateManyMutationInput, TmdbSeasonCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbSeasonCaches to update
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * Limit how many TmdbSeasonCaches to update.
     */
    limit?: number
  }

  /**
   * TmdbSeasonCache updateManyAndReturn
   */
  export type TmdbSeasonCacheUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * The data used to update TmdbSeasonCaches.
     */
    data: XOR<TmdbSeasonCacheUpdateManyMutationInput, TmdbSeasonCacheUncheckedUpdateManyInput>
    /**
     * Filter which TmdbSeasonCaches to update
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * Limit how many TmdbSeasonCaches to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * TmdbSeasonCache upsert
   */
  export type TmdbSeasonCacheUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * The filter to search for the TmdbSeasonCache to update in case it exists.
     */
    where: TmdbSeasonCacheWhereUniqueInput
    /**
     * In case the TmdbSeasonCache found by the `where` argument doesn't exist, create a new TmdbSeasonCache with this data.
     */
    create: XOR<TmdbSeasonCacheCreateInput, TmdbSeasonCacheUncheckedCreateInput>
    /**
     * In case the TmdbSeasonCache was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TmdbSeasonCacheUpdateInput, TmdbSeasonCacheUncheckedUpdateInput>
  }

  /**
   * TmdbSeasonCache delete
   */
  export type TmdbSeasonCacheDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
    /**
     * Filter which TmdbSeasonCache to delete.
     */
    where: TmdbSeasonCacheWhereUniqueInput
  }

  /**
   * TmdbSeasonCache deleteMany
   */
  export type TmdbSeasonCacheDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which TmdbSeasonCaches to delete
     */
    where?: TmdbSeasonCacheWhereInput
    /**
     * Limit how many TmdbSeasonCaches to delete.
     */
    limit?: number
  }

  /**
   * TmdbSeasonCache without action
   */
  export type TmdbSeasonCacheDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the TmdbSeasonCache
     */
    select?: TmdbSeasonCacheSelect<ExtArgs> | null
    /**
     * Omit specific fields from the TmdbSeasonCache
     */
    omit?: TmdbSeasonCacheOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: TmdbSeasonCacheInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const MediaTaskScalarFieldEnum: {
    id: 'id',
    sourcePath: 'sourcePath',
    status: 'status',
    type: 'type',
    fileOperation: 'fileOperation',
    createdAt: 'createdAt',
    finishedAt: 'finishedAt',
    tmdbId: 'tmdbId',
    displayName: 'displayName',
    displayYear: 'displayYear',
    posterPath: 'posterPath'
  };

  export type MediaTaskScalarFieldEnum = (typeof MediaTaskScalarFieldEnum)[keyof typeof MediaTaskScalarFieldEnum]


  export const FileTaskScalarFieldEnum: {
    id: 'id',
    sourcePath: 'sourcePath',
    associatedFiles: 'associatedFiles',
    fileOperation: 'fileOperation',
    fileSize: 'fileSize',
    duration: 'duration',
    resolution: 'resolution',
    fileType: 'fileType',
    status: 'status',
    targetSeason: 'targetSeason',
    targetEpisode: 'targetEpisode',
    targetPath: 'targetPath',
    errorMessage: 'errorMessage',
    mediaTaskId: 'mediaTaskId'
  };

  export type FileTaskScalarFieldEnum = (typeof FileTaskScalarFieldEnum)[keyof typeof FileTaskScalarFieldEnum]


  export const TmdbSearchCacheScalarFieldEnum: {
    query: 'query',
    data: 'data',
    createdAt: 'createdAt'
  };

  export type TmdbSearchCacheScalarFieldEnum = (typeof TmdbSearchCacheScalarFieldEnum)[keyof typeof TmdbSearchCacheScalarFieldEnum]


  export const TmdbMediaCacheScalarFieldEnum: {
    tmdbId: 'tmdbId',
    mediaType: 'mediaType',
    data: 'data',
    createdAt: 'createdAt'
  };

  export type TmdbMediaCacheScalarFieldEnum = (typeof TmdbMediaCacheScalarFieldEnum)[keyof typeof TmdbMediaCacheScalarFieldEnum]


  export const TmdbSeasonCacheScalarFieldEnum: {
    tvId: 'tvId',
    seasonNumber: 'seasonNumber',
    data: 'data',
    createdAt: 'createdAt'
  };

  export type TmdbSeasonCacheScalarFieldEnum = (typeof TmdbSeasonCacheScalarFieldEnum)[keyof typeof TmdbSeasonCacheScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'MediaTaskStatus'
   */
  export type EnumMediaTaskStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'MediaTaskStatus'>
    


  /**
   * Reference to a field of type 'MediaType'
   */
  export type EnumMediaTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'MediaType'>
    


  /**
   * Reference to a field of type 'FileOperation'
   */
  export type EnumFileOperationFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'FileOperation'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'BigInt'
   */
  export type BigIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'BigInt'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'FileType'
   */
  export type EnumFileTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'FileType'>
    


  /**
   * Reference to a field of type 'FileTaskStatus'
   */
  export type EnumFileTaskStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'FileTaskStatus'>
    


  /**
   * Reference to a field of type 'TmdbMediaType'
   */
  export type EnumTmdbMediaTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'TmdbMediaType'>
    
  /**
   * Deep Input Types
   */


  export type MediaTaskWhereInput = {
    AND?: MediaTaskWhereInput | MediaTaskWhereInput[]
    OR?: MediaTaskWhereInput[]
    NOT?: MediaTaskWhereInput | MediaTaskWhereInput[]
    id?: StringFilter<"MediaTask"> | string
    sourcePath?: StringFilter<"MediaTask"> | string
    status?: EnumMediaTaskStatusFilter<"MediaTask"> | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFilter<"MediaTask"> | $Enums.MediaType
    fileOperation?: EnumFileOperationFilter<"MediaTask"> | $Enums.FileOperation
    createdAt?: DateTimeFilter<"MediaTask"> | Date | string
    finishedAt?: DateTimeNullableFilter<"MediaTask"> | Date | string | null
    tmdbId?: IntNullableFilter<"MediaTask"> | number | null
    displayName?: StringNullableFilter<"MediaTask"> | string | null
    displayYear?: IntNullableFilter<"MediaTask"> | number | null
    posterPath?: StringNullableFilter<"MediaTask"> | string | null
    fileTasks?: FileTaskListRelationFilter
  }

  export type MediaTaskOrderByWithRelationInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    status?: SortOrder
    type?: SortOrder
    fileOperation?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrderInput | SortOrder
    tmdbId?: SortOrderInput | SortOrder
    displayName?: SortOrderInput | SortOrder
    displayYear?: SortOrderInput | SortOrder
    posterPath?: SortOrderInput | SortOrder
    fileTasks?: FileTaskOrderByRelationAggregateInput
  }

  export type MediaTaskWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: MediaTaskWhereInput | MediaTaskWhereInput[]
    OR?: MediaTaskWhereInput[]
    NOT?: MediaTaskWhereInput | MediaTaskWhereInput[]
    sourcePath?: StringFilter<"MediaTask"> | string
    status?: EnumMediaTaskStatusFilter<"MediaTask"> | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFilter<"MediaTask"> | $Enums.MediaType
    fileOperation?: EnumFileOperationFilter<"MediaTask"> | $Enums.FileOperation
    createdAt?: DateTimeFilter<"MediaTask"> | Date | string
    finishedAt?: DateTimeNullableFilter<"MediaTask"> | Date | string | null
    tmdbId?: IntNullableFilter<"MediaTask"> | number | null
    displayName?: StringNullableFilter<"MediaTask"> | string | null
    displayYear?: IntNullableFilter<"MediaTask"> | number | null
    posterPath?: StringNullableFilter<"MediaTask"> | string | null
    fileTasks?: FileTaskListRelationFilter
  }, "id">

  export type MediaTaskOrderByWithAggregationInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    status?: SortOrder
    type?: SortOrder
    fileOperation?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrderInput | SortOrder
    tmdbId?: SortOrderInput | SortOrder
    displayName?: SortOrderInput | SortOrder
    displayYear?: SortOrderInput | SortOrder
    posterPath?: SortOrderInput | SortOrder
    _count?: MediaTaskCountOrderByAggregateInput
    _avg?: MediaTaskAvgOrderByAggregateInput
    _max?: MediaTaskMaxOrderByAggregateInput
    _min?: MediaTaskMinOrderByAggregateInput
    _sum?: MediaTaskSumOrderByAggregateInput
  }

  export type MediaTaskScalarWhereWithAggregatesInput = {
    AND?: MediaTaskScalarWhereWithAggregatesInput | MediaTaskScalarWhereWithAggregatesInput[]
    OR?: MediaTaskScalarWhereWithAggregatesInput[]
    NOT?: MediaTaskScalarWhereWithAggregatesInput | MediaTaskScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"MediaTask"> | string
    sourcePath?: StringWithAggregatesFilter<"MediaTask"> | string
    status?: EnumMediaTaskStatusWithAggregatesFilter<"MediaTask"> | $Enums.MediaTaskStatus
    type?: EnumMediaTypeWithAggregatesFilter<"MediaTask"> | $Enums.MediaType
    fileOperation?: EnumFileOperationWithAggregatesFilter<"MediaTask"> | $Enums.FileOperation
    createdAt?: DateTimeWithAggregatesFilter<"MediaTask"> | Date | string
    finishedAt?: DateTimeNullableWithAggregatesFilter<"MediaTask"> | Date | string | null
    tmdbId?: IntNullableWithAggregatesFilter<"MediaTask"> | number | null
    displayName?: StringNullableWithAggregatesFilter<"MediaTask"> | string | null
    displayYear?: IntNullableWithAggregatesFilter<"MediaTask"> | number | null
    posterPath?: StringNullableWithAggregatesFilter<"MediaTask"> | string | null
  }

  export type FileTaskWhereInput = {
    AND?: FileTaskWhereInput | FileTaskWhereInput[]
    OR?: FileTaskWhereInput[]
    NOT?: FileTaskWhereInput | FileTaskWhereInput[]
    id?: StringFilter<"FileTask"> | string
    sourcePath?: StringFilter<"FileTask"> | string
    associatedFiles?: JsonNullableFilter<"FileTask">
    fileOperation?: EnumFileOperationFilter<"FileTask"> | $Enums.FileOperation
    fileSize?: BigIntNullableFilter<"FileTask"> | bigint | number | null
    duration?: FloatNullableFilter<"FileTask"> | number | null
    resolution?: StringNullableFilter<"FileTask"> | string | null
    fileType?: EnumFileTypeFilter<"FileTask"> | $Enums.FileType
    status?: EnumFileTaskStatusFilter<"FileTask"> | $Enums.FileTaskStatus
    targetSeason?: IntNullableFilter<"FileTask"> | number | null
    targetEpisode?: IntNullableFilter<"FileTask"> | number | null
    targetPath?: StringNullableFilter<"FileTask"> | string | null
    errorMessage?: JsonNullableFilter<"FileTask">
    mediaTaskId?: StringFilter<"FileTask"> | string
    mediaTask?: XOR<MediaTaskScalarRelationFilter, MediaTaskWhereInput>
  }

  export type FileTaskOrderByWithRelationInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    associatedFiles?: SortOrderInput | SortOrder
    fileOperation?: SortOrder
    fileSize?: SortOrderInput | SortOrder
    duration?: SortOrderInput | SortOrder
    resolution?: SortOrderInput | SortOrder
    fileType?: SortOrder
    status?: SortOrder
    targetSeason?: SortOrderInput | SortOrder
    targetEpisode?: SortOrderInput | SortOrder
    targetPath?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    mediaTaskId?: SortOrder
    mediaTask?: MediaTaskOrderByWithRelationInput
  }

  export type FileTaskWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    mediaTaskId_sourcePath?: FileTaskMediaTaskIdSourcePathCompoundUniqueInput
    AND?: FileTaskWhereInput | FileTaskWhereInput[]
    OR?: FileTaskWhereInput[]
    NOT?: FileTaskWhereInput | FileTaskWhereInput[]
    sourcePath?: StringFilter<"FileTask"> | string
    associatedFiles?: JsonNullableFilter<"FileTask">
    fileOperation?: EnumFileOperationFilter<"FileTask"> | $Enums.FileOperation
    fileSize?: BigIntNullableFilter<"FileTask"> | bigint | number | null
    duration?: FloatNullableFilter<"FileTask"> | number | null
    resolution?: StringNullableFilter<"FileTask"> | string | null
    fileType?: EnumFileTypeFilter<"FileTask"> | $Enums.FileType
    status?: EnumFileTaskStatusFilter<"FileTask"> | $Enums.FileTaskStatus
    targetSeason?: IntNullableFilter<"FileTask"> | number | null
    targetEpisode?: IntNullableFilter<"FileTask"> | number | null
    targetPath?: StringNullableFilter<"FileTask"> | string | null
    errorMessage?: JsonNullableFilter<"FileTask">
    mediaTaskId?: StringFilter<"FileTask"> | string
    mediaTask?: XOR<MediaTaskScalarRelationFilter, MediaTaskWhereInput>
  }, "id" | "mediaTaskId_sourcePath">

  export type FileTaskOrderByWithAggregationInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    associatedFiles?: SortOrderInput | SortOrder
    fileOperation?: SortOrder
    fileSize?: SortOrderInput | SortOrder
    duration?: SortOrderInput | SortOrder
    resolution?: SortOrderInput | SortOrder
    fileType?: SortOrder
    status?: SortOrder
    targetSeason?: SortOrderInput | SortOrder
    targetEpisode?: SortOrderInput | SortOrder
    targetPath?: SortOrderInput | SortOrder
    errorMessage?: SortOrderInput | SortOrder
    mediaTaskId?: SortOrder
    _count?: FileTaskCountOrderByAggregateInput
    _avg?: FileTaskAvgOrderByAggregateInput
    _max?: FileTaskMaxOrderByAggregateInput
    _min?: FileTaskMinOrderByAggregateInput
    _sum?: FileTaskSumOrderByAggregateInput
  }

  export type FileTaskScalarWhereWithAggregatesInput = {
    AND?: FileTaskScalarWhereWithAggregatesInput | FileTaskScalarWhereWithAggregatesInput[]
    OR?: FileTaskScalarWhereWithAggregatesInput[]
    NOT?: FileTaskScalarWhereWithAggregatesInput | FileTaskScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"FileTask"> | string
    sourcePath?: StringWithAggregatesFilter<"FileTask"> | string
    associatedFiles?: JsonNullableWithAggregatesFilter<"FileTask">
    fileOperation?: EnumFileOperationWithAggregatesFilter<"FileTask"> | $Enums.FileOperation
    fileSize?: BigIntNullableWithAggregatesFilter<"FileTask"> | bigint | number | null
    duration?: FloatNullableWithAggregatesFilter<"FileTask"> | number | null
    resolution?: StringNullableWithAggregatesFilter<"FileTask"> | string | null
    fileType?: EnumFileTypeWithAggregatesFilter<"FileTask"> | $Enums.FileType
    status?: EnumFileTaskStatusWithAggregatesFilter<"FileTask"> | $Enums.FileTaskStatus
    targetSeason?: IntNullableWithAggregatesFilter<"FileTask"> | number | null
    targetEpisode?: IntNullableWithAggregatesFilter<"FileTask"> | number | null
    targetPath?: StringNullableWithAggregatesFilter<"FileTask"> | string | null
    errorMessage?: JsonNullableWithAggregatesFilter<"FileTask">
    mediaTaskId?: StringWithAggregatesFilter<"FileTask"> | string
  }

  export type TmdbSearchCacheWhereInput = {
    AND?: TmdbSearchCacheWhereInput | TmdbSearchCacheWhereInput[]
    OR?: TmdbSearchCacheWhereInput[]
    NOT?: TmdbSearchCacheWhereInput | TmdbSearchCacheWhereInput[]
    query?: StringFilter<"TmdbSearchCache"> | string
    data?: StringFilter<"TmdbSearchCache"> | string
    createdAt?: DateTimeFilter<"TmdbSearchCache"> | Date | string
  }

  export type TmdbSearchCacheOrderByWithRelationInput = {
    query?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSearchCacheWhereUniqueInput = Prisma.AtLeast<{
    query?: string
    AND?: TmdbSearchCacheWhereInput | TmdbSearchCacheWhereInput[]
    OR?: TmdbSearchCacheWhereInput[]
    NOT?: TmdbSearchCacheWhereInput | TmdbSearchCacheWhereInput[]
    data?: StringFilter<"TmdbSearchCache"> | string
    createdAt?: DateTimeFilter<"TmdbSearchCache"> | Date | string
  }, "query">

  export type TmdbSearchCacheOrderByWithAggregationInput = {
    query?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    _count?: TmdbSearchCacheCountOrderByAggregateInput
    _max?: TmdbSearchCacheMaxOrderByAggregateInput
    _min?: TmdbSearchCacheMinOrderByAggregateInput
  }

  export type TmdbSearchCacheScalarWhereWithAggregatesInput = {
    AND?: TmdbSearchCacheScalarWhereWithAggregatesInput | TmdbSearchCacheScalarWhereWithAggregatesInput[]
    OR?: TmdbSearchCacheScalarWhereWithAggregatesInput[]
    NOT?: TmdbSearchCacheScalarWhereWithAggregatesInput | TmdbSearchCacheScalarWhereWithAggregatesInput[]
    query?: StringWithAggregatesFilter<"TmdbSearchCache"> | string
    data?: StringWithAggregatesFilter<"TmdbSearchCache"> | string
    createdAt?: DateTimeWithAggregatesFilter<"TmdbSearchCache"> | Date | string
  }

  export type TmdbMediaCacheWhereInput = {
    AND?: TmdbMediaCacheWhereInput | TmdbMediaCacheWhereInput[]
    OR?: TmdbMediaCacheWhereInput[]
    NOT?: TmdbMediaCacheWhereInput | TmdbMediaCacheWhereInput[]
    tmdbId?: IntFilter<"TmdbMediaCache"> | number
    mediaType?: EnumTmdbMediaTypeFilter<"TmdbMediaCache"> | $Enums.TmdbMediaType
    data?: StringFilter<"TmdbMediaCache"> | string
    createdAt?: DateTimeFilter<"TmdbMediaCache"> | Date | string
    seasons?: TmdbSeasonCacheListRelationFilter
  }

  export type TmdbMediaCacheOrderByWithRelationInput = {
    tmdbId?: SortOrder
    mediaType?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    seasons?: TmdbSeasonCacheOrderByRelationAggregateInput
  }

  export type TmdbMediaCacheWhereUniqueInput = Prisma.AtLeast<{
    tmdbId?: number
    AND?: TmdbMediaCacheWhereInput | TmdbMediaCacheWhereInput[]
    OR?: TmdbMediaCacheWhereInput[]
    NOT?: TmdbMediaCacheWhereInput | TmdbMediaCacheWhereInput[]
    mediaType?: EnumTmdbMediaTypeFilter<"TmdbMediaCache"> | $Enums.TmdbMediaType
    data?: StringFilter<"TmdbMediaCache"> | string
    createdAt?: DateTimeFilter<"TmdbMediaCache"> | Date | string
    seasons?: TmdbSeasonCacheListRelationFilter
  }, "tmdbId">

  export type TmdbMediaCacheOrderByWithAggregationInput = {
    tmdbId?: SortOrder
    mediaType?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    _count?: TmdbMediaCacheCountOrderByAggregateInput
    _avg?: TmdbMediaCacheAvgOrderByAggregateInput
    _max?: TmdbMediaCacheMaxOrderByAggregateInput
    _min?: TmdbMediaCacheMinOrderByAggregateInput
    _sum?: TmdbMediaCacheSumOrderByAggregateInput
  }

  export type TmdbMediaCacheScalarWhereWithAggregatesInput = {
    AND?: TmdbMediaCacheScalarWhereWithAggregatesInput | TmdbMediaCacheScalarWhereWithAggregatesInput[]
    OR?: TmdbMediaCacheScalarWhereWithAggregatesInput[]
    NOT?: TmdbMediaCacheScalarWhereWithAggregatesInput | TmdbMediaCacheScalarWhereWithAggregatesInput[]
    tmdbId?: IntWithAggregatesFilter<"TmdbMediaCache"> | number
    mediaType?: EnumTmdbMediaTypeWithAggregatesFilter<"TmdbMediaCache"> | $Enums.TmdbMediaType
    data?: StringWithAggregatesFilter<"TmdbMediaCache"> | string
    createdAt?: DateTimeWithAggregatesFilter<"TmdbMediaCache"> | Date | string
  }

  export type TmdbSeasonCacheWhereInput = {
    AND?: TmdbSeasonCacheWhereInput | TmdbSeasonCacheWhereInput[]
    OR?: TmdbSeasonCacheWhereInput[]
    NOT?: TmdbSeasonCacheWhereInput | TmdbSeasonCacheWhereInput[]
    tvId?: IntFilter<"TmdbSeasonCache"> | number
    seasonNumber?: IntFilter<"TmdbSeasonCache"> | number
    data?: StringFilter<"TmdbSeasonCache"> | string
    createdAt?: DateTimeFilter<"TmdbSeasonCache"> | Date | string
    tvShow?: XOR<TmdbMediaCacheScalarRelationFilter, TmdbMediaCacheWhereInput>
  }

  export type TmdbSeasonCacheOrderByWithRelationInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    tvShow?: TmdbMediaCacheOrderByWithRelationInput
  }

  export type TmdbSeasonCacheWhereUniqueInput = Prisma.AtLeast<{
    tvId_seasonNumber?: TmdbSeasonCacheTvIdSeasonNumberCompoundUniqueInput
    AND?: TmdbSeasonCacheWhereInput | TmdbSeasonCacheWhereInput[]
    OR?: TmdbSeasonCacheWhereInput[]
    NOT?: TmdbSeasonCacheWhereInput | TmdbSeasonCacheWhereInput[]
    tvId?: IntFilter<"TmdbSeasonCache"> | number
    seasonNumber?: IntFilter<"TmdbSeasonCache"> | number
    data?: StringFilter<"TmdbSeasonCache"> | string
    createdAt?: DateTimeFilter<"TmdbSeasonCache"> | Date | string
    tvShow?: XOR<TmdbMediaCacheScalarRelationFilter, TmdbMediaCacheWhereInput>
  }, "tvId_seasonNumber">

  export type TmdbSeasonCacheOrderByWithAggregationInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
    _count?: TmdbSeasonCacheCountOrderByAggregateInput
    _avg?: TmdbSeasonCacheAvgOrderByAggregateInput
    _max?: TmdbSeasonCacheMaxOrderByAggregateInput
    _min?: TmdbSeasonCacheMinOrderByAggregateInput
    _sum?: TmdbSeasonCacheSumOrderByAggregateInput
  }

  export type TmdbSeasonCacheScalarWhereWithAggregatesInput = {
    AND?: TmdbSeasonCacheScalarWhereWithAggregatesInput | TmdbSeasonCacheScalarWhereWithAggregatesInput[]
    OR?: TmdbSeasonCacheScalarWhereWithAggregatesInput[]
    NOT?: TmdbSeasonCacheScalarWhereWithAggregatesInput | TmdbSeasonCacheScalarWhereWithAggregatesInput[]
    tvId?: IntWithAggregatesFilter<"TmdbSeasonCache"> | number
    seasonNumber?: IntWithAggregatesFilter<"TmdbSeasonCache"> | number
    data?: StringWithAggregatesFilter<"TmdbSeasonCache"> | string
    createdAt?: DateTimeWithAggregatesFilter<"TmdbSeasonCache"> | Date | string
  }

  export type MediaTaskCreateInput = {
    id?: string
    sourcePath: string
    status?: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt?: Date | string
    finishedAt?: Date | string | null
    tmdbId?: number | null
    displayName?: string | null
    displayYear?: number | null
    posterPath?: string | null
    fileTasks?: FileTaskCreateNestedManyWithoutMediaTaskInput
  }

  export type MediaTaskUncheckedCreateInput = {
    id?: string
    sourcePath: string
    status?: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt?: Date | string
    finishedAt?: Date | string | null
    tmdbId?: number | null
    displayName?: string | null
    displayYear?: number | null
    posterPath?: string | null
    fileTasks?: FileTaskUncheckedCreateNestedManyWithoutMediaTaskInput
  }

  export type MediaTaskUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
    fileTasks?: FileTaskUpdateManyWithoutMediaTaskNestedInput
  }

  export type MediaTaskUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
    fileTasks?: FileTaskUncheckedUpdateManyWithoutMediaTaskNestedInput
  }

  export type MediaTaskCreateManyInput = {
    id?: string
    sourcePath: string
    status?: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt?: Date | string
    finishedAt?: Date | string | null
    tmdbId?: number | null
    displayName?: string | null
    displayYear?: number | null
    posterPath?: string | null
  }

  export type MediaTaskUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type MediaTaskUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type FileTaskCreateInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTask: MediaTaskCreateNestedOneWithoutFileTasksInput
  }

  export type FileTaskUncheckedCreateInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTaskId: string
  }

  export type FileTaskUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTask?: MediaTaskUpdateOneRequiredWithoutFileTasksNestedInput
  }

  export type FileTaskUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTaskId?: StringFieldUpdateOperationsInput | string
  }

  export type FileTaskCreateManyInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTaskId: string
  }

  export type FileTaskUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
    mediaTaskId?: StringFieldUpdateOperationsInput | string
  }

  export type TmdbSearchCacheCreateInput = {
    query: string
    data: string
    createdAt?: Date | string
  }

  export type TmdbSearchCacheUncheckedCreateInput = {
    query: string
    data: string
    createdAt?: Date | string
  }

  export type TmdbSearchCacheUpdateInput = {
    query?: StringFieldUpdateOperationsInput | string
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSearchCacheUncheckedUpdateInput = {
    query?: StringFieldUpdateOperationsInput | string
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSearchCacheCreateManyInput = {
    query: string
    data: string
    createdAt?: Date | string
  }

  export type TmdbSearchCacheUpdateManyMutationInput = {
    query?: StringFieldUpdateOperationsInput | string
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSearchCacheUncheckedUpdateManyInput = {
    query?: StringFieldUpdateOperationsInput | string
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbMediaCacheCreateInput = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt?: Date | string
    seasons?: TmdbSeasonCacheCreateNestedManyWithoutTvShowInput
  }

  export type TmdbMediaCacheUncheckedCreateInput = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt?: Date | string
    seasons?: TmdbSeasonCacheUncheckedCreateNestedManyWithoutTvShowInput
  }

  export type TmdbMediaCacheUpdateInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    seasons?: TmdbSeasonCacheUpdateManyWithoutTvShowNestedInput
  }

  export type TmdbMediaCacheUncheckedUpdateInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    seasons?: TmdbSeasonCacheUncheckedUpdateManyWithoutTvShowNestedInput
  }

  export type TmdbMediaCacheCreateManyInput = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt?: Date | string
  }

  export type TmdbMediaCacheUpdateManyMutationInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbMediaCacheUncheckedUpdateManyInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSeasonCacheCreateInput = {
    seasonNumber: number
    data: string
    createdAt?: Date | string
    tvShow: TmdbMediaCacheCreateNestedOneWithoutSeasonsInput
  }

  export type TmdbSeasonCacheUncheckedCreateInput = {
    tvId: number
    seasonNumber: number
    data: string
    createdAt?: Date | string
  }

  export type TmdbSeasonCacheUpdateInput = {
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    tvShow?: TmdbMediaCacheUpdateOneRequiredWithoutSeasonsNestedInput
  }

  export type TmdbSeasonCacheUncheckedUpdateInput = {
    tvId?: IntFieldUpdateOperationsInput | number
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSeasonCacheCreateManyInput = {
    tvId: number
    seasonNumber: number
    data: string
    createdAt?: Date | string
  }

  export type TmdbSeasonCacheUpdateManyMutationInput = {
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSeasonCacheUncheckedUpdateManyInput = {
    tvId?: IntFieldUpdateOperationsInput | number
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type EnumMediaTaskStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaTaskStatus | EnumMediaTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.MediaTaskStatus[]
    notIn?: $Enums.MediaTaskStatus[]
    not?: NestedEnumMediaTaskStatusFilter<$PrismaModel> | $Enums.MediaTaskStatus
  }

  export type EnumMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeFilter<$PrismaModel> | $Enums.MediaType
  }

  export type EnumFileOperationFilter<$PrismaModel = never> = {
    equals?: $Enums.FileOperation | EnumFileOperationFieldRefInput<$PrismaModel>
    in?: $Enums.FileOperation[]
    notIn?: $Enums.FileOperation[]
    not?: NestedEnumFileOperationFilter<$PrismaModel> | $Enums.FileOperation
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type FileTaskListRelationFilter = {
    every?: FileTaskWhereInput
    some?: FileTaskWhereInput
    none?: FileTaskWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type FileTaskOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type MediaTaskCountOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    status?: SortOrder
    type?: SortOrder
    fileOperation?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
    tmdbId?: SortOrder
    displayName?: SortOrder
    displayYear?: SortOrder
    posterPath?: SortOrder
  }

  export type MediaTaskAvgOrderByAggregateInput = {
    tmdbId?: SortOrder
    displayYear?: SortOrder
  }

  export type MediaTaskMaxOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    status?: SortOrder
    type?: SortOrder
    fileOperation?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
    tmdbId?: SortOrder
    displayName?: SortOrder
    displayYear?: SortOrder
    posterPath?: SortOrder
  }

  export type MediaTaskMinOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    status?: SortOrder
    type?: SortOrder
    fileOperation?: SortOrder
    createdAt?: SortOrder
    finishedAt?: SortOrder
    tmdbId?: SortOrder
    displayName?: SortOrder
    displayYear?: SortOrder
    posterPath?: SortOrder
  }

  export type MediaTaskSumOrderByAggregateInput = {
    tmdbId?: SortOrder
    displayYear?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type EnumMediaTaskStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaTaskStatus | EnumMediaTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.MediaTaskStatus[]
    notIn?: $Enums.MediaTaskStatus[]
    not?: NestedEnumMediaTaskStatusWithAggregatesFilter<$PrismaModel> | $Enums.MediaTaskStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTaskStatusFilter<$PrismaModel>
    _max?: NestedEnumMediaTaskStatusFilter<$PrismaModel>
  }

  export type EnumMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.MediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumMediaTypeFilter<$PrismaModel>
  }

  export type EnumFileOperationWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileOperation | EnumFileOperationFieldRefInput<$PrismaModel>
    in?: $Enums.FileOperation[]
    notIn?: $Enums.FileOperation[]
    not?: NestedEnumFileOperationWithAggregatesFilter<$PrismaModel> | $Enums.FileOperation
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileOperationFilter<$PrismaModel>
    _max?: NestedEnumFileOperationFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type BigIntNullableFilter<$PrismaModel = never> = {
    equals?: bigint | number | BigIntFieldRefInput<$PrismaModel> | null
    in?: bigint[] | number[] | null
    notIn?: bigint[] | number[] | null
    lt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    lte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    not?: NestedBigIntNullableFilter<$PrismaModel> | bigint | number | null
  }

  export type FloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type EnumFileTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.FileType | EnumFileTypeFieldRefInput<$PrismaModel>
    in?: $Enums.FileType[]
    notIn?: $Enums.FileType[]
    not?: NestedEnumFileTypeFilter<$PrismaModel> | $Enums.FileType
  }

  export type EnumFileTaskStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.FileTaskStatus | EnumFileTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.FileTaskStatus[]
    notIn?: $Enums.FileTaskStatus[]
    not?: NestedEnumFileTaskStatusFilter<$PrismaModel> | $Enums.FileTaskStatus
  }

  export type MediaTaskScalarRelationFilter = {
    is?: MediaTaskWhereInput
    isNot?: MediaTaskWhereInput
  }

  export type FileTaskMediaTaskIdSourcePathCompoundUniqueInput = {
    mediaTaskId: string
    sourcePath: string
  }

  export type FileTaskCountOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    associatedFiles?: SortOrder
    fileOperation?: SortOrder
    fileSize?: SortOrder
    duration?: SortOrder
    resolution?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    targetSeason?: SortOrder
    targetEpisode?: SortOrder
    targetPath?: SortOrder
    errorMessage?: SortOrder
    mediaTaskId?: SortOrder
  }

  export type FileTaskAvgOrderByAggregateInput = {
    fileSize?: SortOrder
    duration?: SortOrder
    targetSeason?: SortOrder
    targetEpisode?: SortOrder
  }

  export type FileTaskMaxOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    fileOperation?: SortOrder
    fileSize?: SortOrder
    duration?: SortOrder
    resolution?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    targetSeason?: SortOrder
    targetEpisode?: SortOrder
    targetPath?: SortOrder
    mediaTaskId?: SortOrder
  }

  export type FileTaskMinOrderByAggregateInput = {
    id?: SortOrder
    sourcePath?: SortOrder
    fileOperation?: SortOrder
    fileSize?: SortOrder
    duration?: SortOrder
    resolution?: SortOrder
    fileType?: SortOrder
    status?: SortOrder
    targetSeason?: SortOrder
    targetEpisode?: SortOrder
    targetPath?: SortOrder
    mediaTaskId?: SortOrder
  }

  export type FileTaskSumOrderByAggregateInput = {
    fileSize?: SortOrder
    duration?: SortOrder
    targetSeason?: SortOrder
    targetEpisode?: SortOrder
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type BigIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: bigint | number | BigIntFieldRefInput<$PrismaModel> | null
    in?: bigint[] | number[] | null
    notIn?: bigint[] | number[] | null
    lt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    lte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    not?: NestedBigIntNullableWithAggregatesFilter<$PrismaModel> | bigint | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedBigIntNullableFilter<$PrismaModel>
    _min?: NestedBigIntNullableFilter<$PrismaModel>
    _max?: NestedBigIntNullableFilter<$PrismaModel>
  }

  export type FloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type EnumFileTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileType | EnumFileTypeFieldRefInput<$PrismaModel>
    in?: $Enums.FileType[]
    notIn?: $Enums.FileType[]
    not?: NestedEnumFileTypeWithAggregatesFilter<$PrismaModel> | $Enums.FileType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileTypeFilter<$PrismaModel>
    _max?: NestedEnumFileTypeFilter<$PrismaModel>
  }

  export type EnumFileTaskStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileTaskStatus | EnumFileTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.FileTaskStatus[]
    notIn?: $Enums.FileTaskStatus[]
    not?: NestedEnumFileTaskStatusWithAggregatesFilter<$PrismaModel> | $Enums.FileTaskStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileTaskStatusFilter<$PrismaModel>
    _max?: NestedEnumFileTaskStatusFilter<$PrismaModel>
  }

  export type TmdbSearchCacheCountOrderByAggregateInput = {
    query?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSearchCacheMaxOrderByAggregateInput = {
    query?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSearchCacheMinOrderByAggregateInput = {
    query?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type EnumTmdbMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.TmdbMediaType | EnumTmdbMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.TmdbMediaType[]
    notIn?: $Enums.TmdbMediaType[]
    not?: NestedEnumTmdbMediaTypeFilter<$PrismaModel> | $Enums.TmdbMediaType
  }

  export type TmdbSeasonCacheListRelationFilter = {
    every?: TmdbSeasonCacheWhereInput
    some?: TmdbSeasonCacheWhereInput
    none?: TmdbSeasonCacheWhereInput
  }

  export type TmdbSeasonCacheOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type TmdbMediaCacheCountOrderByAggregateInput = {
    tmdbId?: SortOrder
    mediaType?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbMediaCacheAvgOrderByAggregateInput = {
    tmdbId?: SortOrder
  }

  export type TmdbMediaCacheMaxOrderByAggregateInput = {
    tmdbId?: SortOrder
    mediaType?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbMediaCacheMinOrderByAggregateInput = {
    tmdbId?: SortOrder
    mediaType?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbMediaCacheSumOrderByAggregateInput = {
    tmdbId?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type EnumTmdbMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.TmdbMediaType | EnumTmdbMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.TmdbMediaType[]
    notIn?: $Enums.TmdbMediaType[]
    not?: NestedEnumTmdbMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.TmdbMediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumTmdbMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumTmdbMediaTypeFilter<$PrismaModel>
  }

  export type TmdbMediaCacheScalarRelationFilter = {
    is?: TmdbMediaCacheWhereInput
    isNot?: TmdbMediaCacheWhereInput
  }

  export type TmdbSeasonCacheTvIdSeasonNumberCompoundUniqueInput = {
    tvId: number
    seasonNumber: number
  }

  export type TmdbSeasonCacheCountOrderByAggregateInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSeasonCacheAvgOrderByAggregateInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
  }

  export type TmdbSeasonCacheMaxOrderByAggregateInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSeasonCacheMinOrderByAggregateInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
    data?: SortOrder
    createdAt?: SortOrder
  }

  export type TmdbSeasonCacheSumOrderByAggregateInput = {
    tvId?: SortOrder
    seasonNumber?: SortOrder
  }

  export type FileTaskCreateNestedManyWithoutMediaTaskInput = {
    create?: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput> | FileTaskCreateWithoutMediaTaskInput[] | FileTaskUncheckedCreateWithoutMediaTaskInput[]
    connectOrCreate?: FileTaskCreateOrConnectWithoutMediaTaskInput | FileTaskCreateOrConnectWithoutMediaTaskInput[]
    createMany?: FileTaskCreateManyMediaTaskInputEnvelope
    connect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
  }

  export type FileTaskUncheckedCreateNestedManyWithoutMediaTaskInput = {
    create?: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput> | FileTaskCreateWithoutMediaTaskInput[] | FileTaskUncheckedCreateWithoutMediaTaskInput[]
    connectOrCreate?: FileTaskCreateOrConnectWithoutMediaTaskInput | FileTaskCreateOrConnectWithoutMediaTaskInput[]
    createMany?: FileTaskCreateManyMediaTaskInputEnvelope
    connect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type EnumMediaTaskStatusFieldUpdateOperationsInput = {
    set?: $Enums.MediaTaskStatus
  }

  export type EnumMediaTypeFieldUpdateOperationsInput = {
    set?: $Enums.MediaType
  }

  export type EnumFileOperationFieldUpdateOperationsInput = {
    set?: $Enums.FileOperation
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type FileTaskUpdateManyWithoutMediaTaskNestedInput = {
    create?: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput> | FileTaskCreateWithoutMediaTaskInput[] | FileTaskUncheckedCreateWithoutMediaTaskInput[]
    connectOrCreate?: FileTaskCreateOrConnectWithoutMediaTaskInput | FileTaskCreateOrConnectWithoutMediaTaskInput[]
    upsert?: FileTaskUpsertWithWhereUniqueWithoutMediaTaskInput | FileTaskUpsertWithWhereUniqueWithoutMediaTaskInput[]
    createMany?: FileTaskCreateManyMediaTaskInputEnvelope
    set?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    disconnect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    delete?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    connect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    update?: FileTaskUpdateWithWhereUniqueWithoutMediaTaskInput | FileTaskUpdateWithWhereUniqueWithoutMediaTaskInput[]
    updateMany?: FileTaskUpdateManyWithWhereWithoutMediaTaskInput | FileTaskUpdateManyWithWhereWithoutMediaTaskInput[]
    deleteMany?: FileTaskScalarWhereInput | FileTaskScalarWhereInput[]
  }

  export type FileTaskUncheckedUpdateManyWithoutMediaTaskNestedInput = {
    create?: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput> | FileTaskCreateWithoutMediaTaskInput[] | FileTaskUncheckedCreateWithoutMediaTaskInput[]
    connectOrCreate?: FileTaskCreateOrConnectWithoutMediaTaskInput | FileTaskCreateOrConnectWithoutMediaTaskInput[]
    upsert?: FileTaskUpsertWithWhereUniqueWithoutMediaTaskInput | FileTaskUpsertWithWhereUniqueWithoutMediaTaskInput[]
    createMany?: FileTaskCreateManyMediaTaskInputEnvelope
    set?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    disconnect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    delete?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    connect?: FileTaskWhereUniqueInput | FileTaskWhereUniqueInput[]
    update?: FileTaskUpdateWithWhereUniqueWithoutMediaTaskInput | FileTaskUpdateWithWhereUniqueWithoutMediaTaskInput[]
    updateMany?: FileTaskUpdateManyWithWhereWithoutMediaTaskInput | FileTaskUpdateManyWithWhereWithoutMediaTaskInput[]
    deleteMany?: FileTaskScalarWhereInput | FileTaskScalarWhereInput[]
  }

  export type MediaTaskCreateNestedOneWithoutFileTasksInput = {
    create?: XOR<MediaTaskCreateWithoutFileTasksInput, MediaTaskUncheckedCreateWithoutFileTasksInput>
    connectOrCreate?: MediaTaskCreateOrConnectWithoutFileTasksInput
    connect?: MediaTaskWhereUniqueInput
  }

  export type NullableBigIntFieldUpdateOperationsInput = {
    set?: bigint | number | null
    increment?: bigint | number
    decrement?: bigint | number
    multiply?: bigint | number
    divide?: bigint | number
  }

  export type NullableFloatFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type EnumFileTypeFieldUpdateOperationsInput = {
    set?: $Enums.FileType
  }

  export type EnumFileTaskStatusFieldUpdateOperationsInput = {
    set?: $Enums.FileTaskStatus
  }

  export type MediaTaskUpdateOneRequiredWithoutFileTasksNestedInput = {
    create?: XOR<MediaTaskCreateWithoutFileTasksInput, MediaTaskUncheckedCreateWithoutFileTasksInput>
    connectOrCreate?: MediaTaskCreateOrConnectWithoutFileTasksInput
    upsert?: MediaTaskUpsertWithoutFileTasksInput
    connect?: MediaTaskWhereUniqueInput
    update?: XOR<XOR<MediaTaskUpdateToOneWithWhereWithoutFileTasksInput, MediaTaskUpdateWithoutFileTasksInput>, MediaTaskUncheckedUpdateWithoutFileTasksInput>
  }

  export type TmdbSeasonCacheCreateNestedManyWithoutTvShowInput = {
    create?: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput> | TmdbSeasonCacheCreateWithoutTvShowInput[] | TmdbSeasonCacheUncheckedCreateWithoutTvShowInput[]
    connectOrCreate?: TmdbSeasonCacheCreateOrConnectWithoutTvShowInput | TmdbSeasonCacheCreateOrConnectWithoutTvShowInput[]
    createMany?: TmdbSeasonCacheCreateManyTvShowInputEnvelope
    connect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
  }

  export type TmdbSeasonCacheUncheckedCreateNestedManyWithoutTvShowInput = {
    create?: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput> | TmdbSeasonCacheCreateWithoutTvShowInput[] | TmdbSeasonCacheUncheckedCreateWithoutTvShowInput[]
    connectOrCreate?: TmdbSeasonCacheCreateOrConnectWithoutTvShowInput | TmdbSeasonCacheCreateOrConnectWithoutTvShowInput[]
    createMany?: TmdbSeasonCacheCreateManyTvShowInputEnvelope
    connect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type EnumTmdbMediaTypeFieldUpdateOperationsInput = {
    set?: $Enums.TmdbMediaType
  }

  export type TmdbSeasonCacheUpdateManyWithoutTvShowNestedInput = {
    create?: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput> | TmdbSeasonCacheCreateWithoutTvShowInput[] | TmdbSeasonCacheUncheckedCreateWithoutTvShowInput[]
    connectOrCreate?: TmdbSeasonCacheCreateOrConnectWithoutTvShowInput | TmdbSeasonCacheCreateOrConnectWithoutTvShowInput[]
    upsert?: TmdbSeasonCacheUpsertWithWhereUniqueWithoutTvShowInput | TmdbSeasonCacheUpsertWithWhereUniqueWithoutTvShowInput[]
    createMany?: TmdbSeasonCacheCreateManyTvShowInputEnvelope
    set?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    disconnect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    delete?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    connect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    update?: TmdbSeasonCacheUpdateWithWhereUniqueWithoutTvShowInput | TmdbSeasonCacheUpdateWithWhereUniqueWithoutTvShowInput[]
    updateMany?: TmdbSeasonCacheUpdateManyWithWhereWithoutTvShowInput | TmdbSeasonCacheUpdateManyWithWhereWithoutTvShowInput[]
    deleteMany?: TmdbSeasonCacheScalarWhereInput | TmdbSeasonCacheScalarWhereInput[]
  }

  export type TmdbSeasonCacheUncheckedUpdateManyWithoutTvShowNestedInput = {
    create?: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput> | TmdbSeasonCacheCreateWithoutTvShowInput[] | TmdbSeasonCacheUncheckedCreateWithoutTvShowInput[]
    connectOrCreate?: TmdbSeasonCacheCreateOrConnectWithoutTvShowInput | TmdbSeasonCacheCreateOrConnectWithoutTvShowInput[]
    upsert?: TmdbSeasonCacheUpsertWithWhereUniqueWithoutTvShowInput | TmdbSeasonCacheUpsertWithWhereUniqueWithoutTvShowInput[]
    createMany?: TmdbSeasonCacheCreateManyTvShowInputEnvelope
    set?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    disconnect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    delete?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    connect?: TmdbSeasonCacheWhereUniqueInput | TmdbSeasonCacheWhereUniqueInput[]
    update?: TmdbSeasonCacheUpdateWithWhereUniqueWithoutTvShowInput | TmdbSeasonCacheUpdateWithWhereUniqueWithoutTvShowInput[]
    updateMany?: TmdbSeasonCacheUpdateManyWithWhereWithoutTvShowInput | TmdbSeasonCacheUpdateManyWithWhereWithoutTvShowInput[]
    deleteMany?: TmdbSeasonCacheScalarWhereInput | TmdbSeasonCacheScalarWhereInput[]
  }

  export type TmdbMediaCacheCreateNestedOneWithoutSeasonsInput = {
    create?: XOR<TmdbMediaCacheCreateWithoutSeasonsInput, TmdbMediaCacheUncheckedCreateWithoutSeasonsInput>
    connectOrCreate?: TmdbMediaCacheCreateOrConnectWithoutSeasonsInput
    connect?: TmdbMediaCacheWhereUniqueInput
  }

  export type TmdbMediaCacheUpdateOneRequiredWithoutSeasonsNestedInput = {
    create?: XOR<TmdbMediaCacheCreateWithoutSeasonsInput, TmdbMediaCacheUncheckedCreateWithoutSeasonsInput>
    connectOrCreate?: TmdbMediaCacheCreateOrConnectWithoutSeasonsInput
    upsert?: TmdbMediaCacheUpsertWithoutSeasonsInput
    connect?: TmdbMediaCacheWhereUniqueInput
    update?: XOR<XOR<TmdbMediaCacheUpdateToOneWithWhereWithoutSeasonsInput, TmdbMediaCacheUpdateWithoutSeasonsInput>, TmdbMediaCacheUncheckedUpdateWithoutSeasonsInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedEnumMediaTaskStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaTaskStatus | EnumMediaTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.MediaTaskStatus[]
    notIn?: $Enums.MediaTaskStatus[]
    not?: NestedEnumMediaTaskStatusFilter<$PrismaModel> | $Enums.MediaTaskStatus
  }

  export type NestedEnumMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeFilter<$PrismaModel> | $Enums.MediaType
  }

  export type NestedEnumFileOperationFilter<$PrismaModel = never> = {
    equals?: $Enums.FileOperation | EnumFileOperationFieldRefInput<$PrismaModel>
    in?: $Enums.FileOperation[]
    notIn?: $Enums.FileOperation[]
    not?: NestedEnumFileOperationFilter<$PrismaModel> | $Enums.FileOperation
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedEnumMediaTaskStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaTaskStatus | EnumMediaTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.MediaTaskStatus[]
    notIn?: $Enums.MediaTaskStatus[]
    not?: NestedEnumMediaTaskStatusWithAggregatesFilter<$PrismaModel> | $Enums.MediaTaskStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTaskStatusFilter<$PrismaModel>
    _max?: NestedEnumMediaTaskStatusFilter<$PrismaModel>
  }

  export type NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.MediaType | EnumMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.MediaType[]
    notIn?: $Enums.MediaType[]
    not?: NestedEnumMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.MediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumMediaTypeFilter<$PrismaModel>
  }

  export type NestedEnumFileOperationWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileOperation | EnumFileOperationFieldRefInput<$PrismaModel>
    in?: $Enums.FileOperation[]
    notIn?: $Enums.FileOperation[]
    not?: NestedEnumFileOperationWithAggregatesFilter<$PrismaModel> | $Enums.FileOperation
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileOperationFilter<$PrismaModel>
    _max?: NestedEnumFileOperationFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | null
    notIn?: Date[] | string[] | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | null
    notIn?: string[] | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedBigIntNullableFilter<$PrismaModel = never> = {
    equals?: bigint | number | BigIntFieldRefInput<$PrismaModel> | null
    in?: bigint[] | number[] | null
    notIn?: bigint[] | number[] | null
    lt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    lte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    not?: NestedBigIntNullableFilter<$PrismaModel> | bigint | number | null
  }

  export type NestedEnumFileTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.FileType | EnumFileTypeFieldRefInput<$PrismaModel>
    in?: $Enums.FileType[]
    notIn?: $Enums.FileType[]
    not?: NestedEnumFileTypeFilter<$PrismaModel> | $Enums.FileType
  }

  export type NestedEnumFileTaskStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.FileTaskStatus | EnumFileTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.FileTaskStatus[]
    notIn?: $Enums.FileTaskStatus[]
    not?: NestedEnumFileTaskStatusFilter<$PrismaModel> | $Enums.FileTaskStatus
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedBigIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: bigint | number | BigIntFieldRefInput<$PrismaModel> | null
    in?: bigint[] | number[] | null
    notIn?: bigint[] | number[] | null
    lt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    lte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gt?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    gte?: bigint | number | BigIntFieldRefInput<$PrismaModel>
    not?: NestedBigIntNullableWithAggregatesFilter<$PrismaModel> | bigint | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedBigIntNullableFilter<$PrismaModel>
    _min?: NestedBigIntNullableFilter<$PrismaModel>
    _max?: NestedBigIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedFloatNullableFilter<$PrismaModel>
    _min?: NestedFloatNullableFilter<$PrismaModel>
    _max?: NestedFloatNullableFilter<$PrismaModel>
  }

  export type NestedEnumFileTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileType | EnumFileTypeFieldRefInput<$PrismaModel>
    in?: $Enums.FileType[]
    notIn?: $Enums.FileType[]
    not?: NestedEnumFileTypeWithAggregatesFilter<$PrismaModel> | $Enums.FileType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileTypeFilter<$PrismaModel>
    _max?: NestedEnumFileTypeFilter<$PrismaModel>
  }

  export type NestedEnumFileTaskStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.FileTaskStatus | EnumFileTaskStatusFieldRefInput<$PrismaModel>
    in?: $Enums.FileTaskStatus[]
    notIn?: $Enums.FileTaskStatus[]
    not?: NestedEnumFileTaskStatusWithAggregatesFilter<$PrismaModel> | $Enums.FileTaskStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumFileTaskStatusFilter<$PrismaModel>
    _max?: NestedEnumFileTaskStatusFilter<$PrismaModel>
  }

  export type NestedEnumTmdbMediaTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.TmdbMediaType | EnumTmdbMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.TmdbMediaType[]
    notIn?: $Enums.TmdbMediaType[]
    not?: NestedEnumTmdbMediaTypeFilter<$PrismaModel> | $Enums.TmdbMediaType
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedEnumTmdbMediaTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.TmdbMediaType | EnumTmdbMediaTypeFieldRefInput<$PrismaModel>
    in?: $Enums.TmdbMediaType[]
    notIn?: $Enums.TmdbMediaType[]
    not?: NestedEnumTmdbMediaTypeWithAggregatesFilter<$PrismaModel> | $Enums.TmdbMediaType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumTmdbMediaTypeFilter<$PrismaModel>
    _max?: NestedEnumTmdbMediaTypeFilter<$PrismaModel>
  }

  export type FileTaskCreateWithoutMediaTaskInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskUncheckedCreateWithoutMediaTaskInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskCreateOrConnectWithoutMediaTaskInput = {
    where: FileTaskWhereUniqueInput
    create: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput>
  }

  export type FileTaskCreateManyMediaTaskInputEnvelope = {
    data: FileTaskCreateManyMediaTaskInput | FileTaskCreateManyMediaTaskInput[]
  }

  export type FileTaskUpsertWithWhereUniqueWithoutMediaTaskInput = {
    where: FileTaskWhereUniqueInput
    update: XOR<FileTaskUpdateWithoutMediaTaskInput, FileTaskUncheckedUpdateWithoutMediaTaskInput>
    create: XOR<FileTaskCreateWithoutMediaTaskInput, FileTaskUncheckedCreateWithoutMediaTaskInput>
  }

  export type FileTaskUpdateWithWhereUniqueWithoutMediaTaskInput = {
    where: FileTaskWhereUniqueInput
    data: XOR<FileTaskUpdateWithoutMediaTaskInput, FileTaskUncheckedUpdateWithoutMediaTaskInput>
  }

  export type FileTaskUpdateManyWithWhereWithoutMediaTaskInput = {
    where: FileTaskScalarWhereInput
    data: XOR<FileTaskUpdateManyMutationInput, FileTaskUncheckedUpdateManyWithoutMediaTaskInput>
  }

  export type FileTaskScalarWhereInput = {
    AND?: FileTaskScalarWhereInput | FileTaskScalarWhereInput[]
    OR?: FileTaskScalarWhereInput[]
    NOT?: FileTaskScalarWhereInput | FileTaskScalarWhereInput[]
    id?: StringFilter<"FileTask"> | string
    sourcePath?: StringFilter<"FileTask"> | string
    associatedFiles?: JsonNullableFilter<"FileTask">
    fileOperation?: EnumFileOperationFilter<"FileTask"> | $Enums.FileOperation
    fileSize?: BigIntNullableFilter<"FileTask"> | bigint | number | null
    duration?: FloatNullableFilter<"FileTask"> | number | null
    resolution?: StringNullableFilter<"FileTask"> | string | null
    fileType?: EnumFileTypeFilter<"FileTask"> | $Enums.FileType
    status?: EnumFileTaskStatusFilter<"FileTask"> | $Enums.FileTaskStatus
    targetSeason?: IntNullableFilter<"FileTask"> | number | null
    targetEpisode?: IntNullableFilter<"FileTask"> | number | null
    targetPath?: StringNullableFilter<"FileTask"> | string | null
    errorMessage?: JsonNullableFilter<"FileTask">
    mediaTaskId?: StringFilter<"FileTask"> | string
  }

  export type MediaTaskCreateWithoutFileTasksInput = {
    id?: string
    sourcePath: string
    status?: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt?: Date | string
    finishedAt?: Date | string | null
    tmdbId?: number | null
    displayName?: string | null
    displayYear?: number | null
    posterPath?: string | null
  }

  export type MediaTaskUncheckedCreateWithoutFileTasksInput = {
    id?: string
    sourcePath: string
    status?: $Enums.MediaTaskStatus
    type: $Enums.MediaType
    fileOperation: $Enums.FileOperation
    createdAt?: Date | string
    finishedAt?: Date | string | null
    tmdbId?: number | null
    displayName?: string | null
    displayYear?: number | null
    posterPath?: string | null
  }

  export type MediaTaskCreateOrConnectWithoutFileTasksInput = {
    where: MediaTaskWhereUniqueInput
    create: XOR<MediaTaskCreateWithoutFileTasksInput, MediaTaskUncheckedCreateWithoutFileTasksInput>
  }

  export type MediaTaskUpsertWithoutFileTasksInput = {
    update: XOR<MediaTaskUpdateWithoutFileTasksInput, MediaTaskUncheckedUpdateWithoutFileTasksInput>
    create: XOR<MediaTaskCreateWithoutFileTasksInput, MediaTaskUncheckedCreateWithoutFileTasksInput>
    where?: MediaTaskWhereInput
  }

  export type MediaTaskUpdateToOneWithWhereWithoutFileTasksInput = {
    where?: MediaTaskWhereInput
    data: XOR<MediaTaskUpdateWithoutFileTasksInput, MediaTaskUncheckedUpdateWithoutFileTasksInput>
  }

  export type MediaTaskUpdateWithoutFileTasksInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type MediaTaskUncheckedUpdateWithoutFileTasksInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    status?: EnumMediaTaskStatusFieldUpdateOperationsInput | $Enums.MediaTaskStatus
    type?: EnumMediaTypeFieldUpdateOperationsInput | $Enums.MediaType
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    finishedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tmdbId?: NullableIntFieldUpdateOperationsInput | number | null
    displayName?: NullableStringFieldUpdateOperationsInput | string | null
    displayYear?: NullableIntFieldUpdateOperationsInput | number | null
    posterPath?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type TmdbSeasonCacheCreateWithoutTvShowInput = {
    seasonNumber: number
    data: string
    createdAt?: Date | string
  }

  export type TmdbSeasonCacheUncheckedCreateWithoutTvShowInput = {
    seasonNumber: number
    data: string
    createdAt?: Date | string
  }

  export type TmdbSeasonCacheCreateOrConnectWithoutTvShowInput = {
    where: TmdbSeasonCacheWhereUniqueInput
    create: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput>
  }

  export type TmdbSeasonCacheCreateManyTvShowInputEnvelope = {
    data: TmdbSeasonCacheCreateManyTvShowInput | TmdbSeasonCacheCreateManyTvShowInput[]
  }

  export type TmdbSeasonCacheUpsertWithWhereUniqueWithoutTvShowInput = {
    where: TmdbSeasonCacheWhereUniqueInput
    update: XOR<TmdbSeasonCacheUpdateWithoutTvShowInput, TmdbSeasonCacheUncheckedUpdateWithoutTvShowInput>
    create: XOR<TmdbSeasonCacheCreateWithoutTvShowInput, TmdbSeasonCacheUncheckedCreateWithoutTvShowInput>
  }

  export type TmdbSeasonCacheUpdateWithWhereUniqueWithoutTvShowInput = {
    where: TmdbSeasonCacheWhereUniqueInput
    data: XOR<TmdbSeasonCacheUpdateWithoutTvShowInput, TmdbSeasonCacheUncheckedUpdateWithoutTvShowInput>
  }

  export type TmdbSeasonCacheUpdateManyWithWhereWithoutTvShowInput = {
    where: TmdbSeasonCacheScalarWhereInput
    data: XOR<TmdbSeasonCacheUpdateManyMutationInput, TmdbSeasonCacheUncheckedUpdateManyWithoutTvShowInput>
  }

  export type TmdbSeasonCacheScalarWhereInput = {
    AND?: TmdbSeasonCacheScalarWhereInput | TmdbSeasonCacheScalarWhereInput[]
    OR?: TmdbSeasonCacheScalarWhereInput[]
    NOT?: TmdbSeasonCacheScalarWhereInput | TmdbSeasonCacheScalarWhereInput[]
    tvId?: IntFilter<"TmdbSeasonCache"> | number
    seasonNumber?: IntFilter<"TmdbSeasonCache"> | number
    data?: StringFilter<"TmdbSeasonCache"> | string
    createdAt?: DateTimeFilter<"TmdbSeasonCache"> | Date | string
  }

  export type TmdbMediaCacheCreateWithoutSeasonsInput = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt?: Date | string
  }

  export type TmdbMediaCacheUncheckedCreateWithoutSeasonsInput = {
    tmdbId: number
    mediaType: $Enums.TmdbMediaType
    data: string
    createdAt?: Date | string
  }

  export type TmdbMediaCacheCreateOrConnectWithoutSeasonsInput = {
    where: TmdbMediaCacheWhereUniqueInput
    create: XOR<TmdbMediaCacheCreateWithoutSeasonsInput, TmdbMediaCacheUncheckedCreateWithoutSeasonsInput>
  }

  export type TmdbMediaCacheUpsertWithoutSeasonsInput = {
    update: XOR<TmdbMediaCacheUpdateWithoutSeasonsInput, TmdbMediaCacheUncheckedUpdateWithoutSeasonsInput>
    create: XOR<TmdbMediaCacheCreateWithoutSeasonsInput, TmdbMediaCacheUncheckedCreateWithoutSeasonsInput>
    where?: TmdbMediaCacheWhereInput
  }

  export type TmdbMediaCacheUpdateToOneWithWhereWithoutSeasonsInput = {
    where?: TmdbMediaCacheWhereInput
    data: XOR<TmdbMediaCacheUpdateWithoutSeasonsInput, TmdbMediaCacheUncheckedUpdateWithoutSeasonsInput>
  }

  export type TmdbMediaCacheUpdateWithoutSeasonsInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbMediaCacheUncheckedUpdateWithoutSeasonsInput = {
    tmdbId?: IntFieldUpdateOperationsInput | number
    mediaType?: EnumTmdbMediaTypeFieldUpdateOperationsInput | $Enums.TmdbMediaType
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type FileTaskCreateManyMediaTaskInput = {
    id?: string
    sourcePath: string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation: $Enums.FileOperation
    fileSize?: bigint | number | null
    duration?: number | null
    resolution?: string | null
    fileType?: $Enums.FileType
    status?: $Enums.FileTaskStatus
    targetSeason?: number | null
    targetEpisode?: number | null
    targetPath?: string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskUpdateWithoutMediaTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskUncheckedUpdateWithoutMediaTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type FileTaskUncheckedUpdateManyWithoutMediaTaskInput = {
    id?: StringFieldUpdateOperationsInput | string
    sourcePath?: StringFieldUpdateOperationsInput | string
    associatedFiles?: NullableJsonNullValueInput | InputJsonValue
    fileOperation?: EnumFileOperationFieldUpdateOperationsInput | $Enums.FileOperation
    fileSize?: NullableBigIntFieldUpdateOperationsInput | bigint | number | null
    duration?: NullableFloatFieldUpdateOperationsInput | number | null
    resolution?: NullableStringFieldUpdateOperationsInput | string | null
    fileType?: EnumFileTypeFieldUpdateOperationsInput | $Enums.FileType
    status?: EnumFileTaskStatusFieldUpdateOperationsInput | $Enums.FileTaskStatus
    targetSeason?: NullableIntFieldUpdateOperationsInput | number | null
    targetEpisode?: NullableIntFieldUpdateOperationsInput | number | null
    targetPath?: NullableStringFieldUpdateOperationsInput | string | null
    errorMessage?: NullableJsonNullValueInput | InputJsonValue
  }

  export type TmdbSeasonCacheCreateManyTvShowInput = {
    seasonNumber: number
    data: string
    createdAt?: Date | string
  }

  export type TmdbSeasonCacheUpdateWithoutTvShowInput = {
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSeasonCacheUncheckedUpdateWithoutTvShowInput = {
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TmdbSeasonCacheUncheckedUpdateManyWithoutTvShowInput = {
    seasonNumber?: IntFieldUpdateOperationsInput | number
    data?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}