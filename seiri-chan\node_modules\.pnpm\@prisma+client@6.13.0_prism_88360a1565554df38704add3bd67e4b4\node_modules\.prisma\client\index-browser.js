
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.13.0
 * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
 */
Prisma.prismaVersion = {
  client: "6.13.0",
  engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.MediaTaskScalarFieldEnum = {
  id: 'id',
  sourcePath: 'sourcePath',
  status: 'status',
  type: 'type',
  fileOperation: 'fileOperation',
  createdAt: 'createdAt',
  finishedAt: 'finishedAt',
  tmdbId: 'tmdbId',
  displayName: 'displayName',
  displayYear: 'displayYear',
  posterPath: 'posterPath'
};

exports.Prisma.FileTaskScalarFieldEnum = {
  id: 'id',
  sourcePath: 'sourcePath',
  associatedFiles: 'associatedFiles',
  fileOperation: 'fileOperation',
  fileSize: 'fileSize',
  duration: 'duration',
  resolution: 'resolution',
  fileType: 'fileType',
  status: 'status',
  targetSeason: 'targetSeason',
  targetEpisode: 'targetEpisode',
  targetPath: 'targetPath',
  errorMessage: 'errorMessage',
  mediaTaskId: 'mediaTaskId'
};

exports.Prisma.TmdbSearchCacheScalarFieldEnum = {
  query: 'query',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.TmdbMediaCacheScalarFieldEnum = {
  tmdbId: 'tmdbId',
  mediaType: 'mediaType',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.TmdbSeasonCacheScalarFieldEnum = {
  tvId: 'tvId',
  seasonNumber: 'seasonNumber',
  data: 'data',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.MediaTaskStatus = exports.$Enums.MediaTaskStatus = {
  PENDING: 'PENDING',
  SCANNING: 'SCANNING',
  ANALYSIS_FAILED: 'ANALYSIS_FAILED',
  ANALYSIS_AWAITING_USER: 'ANALYSIS_AWAITING_USER',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR'
};

exports.MediaType = exports.$Enums.MediaType = {
  ANIME: 'ANIME',
  TV: 'TV',
  MOVIE: 'MOVIE',
  ANIME_MOVIE: 'ANIME_MOVIE'
};

exports.FileOperation = exports.$Enums.FileOperation = {
  HARDLINK: 'HARDLINK',
  SOFTLINK: 'SOFTLINK',
  COPY: 'COPY',
  MOVE: 'MOVE',
  SKIP: 'SKIP'
};

exports.FileType = exports.$Enums.FileType = {
  VIDEO_MAIN: 'VIDEO_MAIN',
  VIDEO_SPECIAL: 'VIDEO_SPECIAL',
  VIDEO_TRAILER: 'VIDEO_TRAILER',
  VIDEO_EXTRA: 'VIDEO_EXTRA',
  VIDEO_UNKNOWN: 'VIDEO_UNKNOWN',
  SUBTITLE: 'SUBTITLE',
  FONT: 'FONT'
};

exports.FileTaskStatus = exports.$Enums.FileTaskStatus = {
  PENDING: 'PENDING',
  IGNORED: 'IGNORED',
  MAPPED: 'MAPPED',
  MISSING: 'MISSING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FILE_NOT_EXIST: 'FILE_NOT_EXIST',
  FAILED: 'FAILED',
  FATAL_ERROR: 'FATAL_ERROR',
  CONFIRMATION_REQUIRED: 'CONFIRMATION_REQUIRED'
};

exports.TmdbMediaType = exports.$Enums.TmdbMediaType = {
  MOVIE: 'MOVIE',
  TV: 'TV'
};

exports.Prisma.ModelName = {
  MediaTask: 'MediaTask',
  FileTask: 'FileTask',
  TmdbSearchCache: 'TmdbSearchCache',
  TmdbMediaCache: 'TmdbMediaCache',
  TmdbSeasonCache: 'TmdbSeasonCache'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
